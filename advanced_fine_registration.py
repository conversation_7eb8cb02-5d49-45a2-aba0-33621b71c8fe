"""
高级的基于粗配准矩阵的精配准脚本。
使用多种配准算法和优化技术。
目标：达到PV值90μm和RMS值20μm的精度要求。
"""
import os
import numpy as np
import open3d as o3d
from typing import Dict, Any
import time
from scipy.optimize import minimize

def load_point_cloud(file_path: str) -> o3d.geometry.PointCloud:
    """加载点云数据。"""
    pcd = o3d.geometry.PointCloud()
    
    try:
        with open(file_path, 'r') as f:
            lines = f.readlines()
        
        points = []
        for line in lines:
            values = line.strip().split()
            if len(values) >= 3:
                x, y, z = float(values[0]), float(values[1]), float(values[2])
                points.append([x, y, z])
        
        points = np.array(points)
        pcd.points = o3d.utility.Vector3dVector(points)
        print(f"成功加载点云，共 {len(points)} 个点")
    except Exception as e:
        raise ValueError(f"无法加载点云文件: {e}")
    
    return pcd

def load_transformation_matrix(file_path: str) -> np.ndarray:
    """加载变换矩阵。"""
    try:
        transformation = np.loadtxt(file_path)
        if transformation.shape != (4, 4):
            raise ValueError(f"变换矩阵应该是4x4，但得到了{transformation.shape}")
        print(f"成功加载粗配准矩阵")
        return transformation
    except Exception as e:
        raise ValueError(f"无法加载变换矩阵: {e}")

def calculate_metrics(source_points: np.ndarray, target_points: np.ndarray, 
                      trim_percentage: float = 0.0) -> Dict[str, Any]:
    """计算配准指标：PV值和RMS。"""
    # 为目标点云构建KD树
    target_tree = o3d.geometry.KDTreeFlann()
    target_pcd = o3d.geometry.PointCloud()
    target_pcd.points = o3d.utility.Vector3dVector(target_points)
    target_tree.set_geometry(target_pcd)
    
    # 计算点到点的距离
    distances = []
    for point in source_points:
        _, _, dist = target_tree.search_knn_vector_3d(point, 1)
        distances.append(np.sqrt(dist[0]))
    
    distances = np.array(distances)
    
    # 如果需要裁剪，去除最远的点
    if trim_percentage > 0:
        sorted_distances = np.sort(distances)
        trim_index = int(len(sorted_distances) * (1 - trim_percentage))
        trimmed_distances = sorted_distances[:trim_index]
    else:
        trimmed_distances = distances
    
    # 计算PV值（峰谷值，90百分位数）
    pv_value = np.percentile(trimmed_distances, 90) * 1000  # 转换为微米
    
    # 计算RMS（均方根）
    rms = np.sqrt(np.mean(trimmed_distances ** 2)) * 1000  # 转换为微米
    
    return {
        'pv_value': pv_value,
        'rms': rms,
        'distances': trimmed_distances,
        'original_distances': distances
    }

def transformation_matrix_to_params(T: np.ndarray) -> np.ndarray:
    """将4x4变换矩阵转换为6个参数（3个旋转角度 + 3个平移）。"""
    # 提取旋转矩阵
    R = T[:3, :3]
    
    # 提取欧拉角（ZYX顺序）
    sy = np.sqrt(R[0, 0] * R[0, 0] + R[1, 0] * R[1, 0])
    singular = sy < 1e-6
    
    if not singular:
        x = np.arctan2(R[2, 1], R[2, 2])
        y = np.arctan2(-R[2, 0], sy)
        z = np.arctan2(R[1, 0], R[0, 0])
    else:
        x = np.arctan2(-R[1, 2], R[1, 1])
        y = np.arctan2(-R[2, 0], sy)
        z = 0
    
    # 提取平移
    t = T[:3, 3]
    
    return np.array([x, y, z, t[0], t[1], t[2]])

def params_to_transformation_matrix(params: np.ndarray) -> np.ndarray:
    """将6个参数转换为4x4变换矩阵。"""
    rx, ry, rz, tx, ty, tz = params
    
    # 创建旋转矩阵
    Rx = np.array([[1, 0, 0],
                   [0, np.cos(rx), -np.sin(rx)],
                   [0, np.sin(rx), np.cos(rx)]])
    
    Ry = np.array([[np.cos(ry), 0, np.sin(ry)],
                   [0, 1, 0],
                   [-np.sin(ry), 0, np.cos(ry)]])
    
    Rz = np.array([[np.cos(rz), -np.sin(rz), 0],
                   [np.sin(rz), np.cos(rz), 0],
                   [0, 0, 1]])
    
    R = Rz @ Ry @ Rx
    
    # 创建变换矩阵
    T = np.eye(4)
    T[:3, :3] = R
    T[:3, 3] = [tx, ty, tz]
    
    return T

def nonlinear_optimization(source_points: np.ndarray, target_points: np.ndarray, 
                          initial_transformation: np.ndarray) -> np.ndarray:
    """使用非线性优化进行精配准。"""
    # 为目标点云构建KD树
    target_tree = o3d.geometry.KDTreeFlann()
    target_pcd = o3d.geometry.PointCloud()
    target_pcd.points = o3d.utility.Vector3dVector(target_points)
    target_tree.set_geometry(target_pcd)
    
    def objective_function(params):
        """目标函数：最小化点到点的距离。"""
        T = params_to_transformation_matrix(params)
        
        # 变换源点云
        transformed_points = (T[:3, :3] @ source_points.T + T[:3, 3:4]).T
        
        # 计算距离
        total_distance = 0.0
        for point in transformed_points:
            _, _, dist = target_tree.search_knn_vector_3d(point, 1)
            total_distance += dist[0]
        
        return total_distance / len(transformed_points)
    
    # 将初始变换矩阵转换为参数
    initial_params = transformation_matrix_to_params(initial_transformation)
    
    # 执行优化
    print("    执行非线性优化...")
    result = minimize(objective_function, initial_params, method='Powell', 
                     options={'maxiter': 100, 'disp': False})
    
    # 转换回变换矩阵
    optimized_transformation = params_to_transformation_matrix(result.x)
    
    return optimized_transformation

def main():
    """主函数。"""
    # 设置文件路径
    source_file = '点云数据.txt'
    target_file = '面型扫描.txt'
    coarse_rt_file = '粗配准RT.txt'
    results_dir = 'advanced_fine_registration_results'
    
    # 创建结果目录
    os.makedirs(results_dir, exist_ok=True)
    
    print("=== 高级的基于粗配准矩阵的精配准 ===")
    
    # 加载点云
    print("\n1. 加载点云数据...")
    source = load_point_cloud(source_file)
    target = load_point_cloud(target_file)
    
    # 加载粗配准矩阵
    print("\n2. 加载粗配准矩阵...")
    coarse_transformation = load_transformation_matrix(coarse_rt_file)
    
    # 应用粗配准变换
    print("\n3. 应用粗配准变换...")
    source_coarse = o3d.geometry.PointCloud()
    source_coarse.points = o3d.utility.Vector3dVector(np.asarray(source.points))
    source_coarse.transform(coarse_transformation)
    
    # 计算粗配准后的指标
    print("\n4. 计算粗配准后的指标...")
    coarse_metrics = calculate_metrics(np.asarray(source_coarse.points), np.asarray(target.points))
    print(f"粗配准后指标:")
    print(f"  PV值: {coarse_metrics['pv_value']:.2f} 微米")
    print(f"  RMS: {coarse_metrics['rms']:.2f} 微米")
    
    # 预处理点云
    print("\n5. 预处理点云...")
    
    # 移除离群点
    source_clean, _ = source_coarse.remove_statistical_outlier(nb_neighbors=20, std_ratio=2.0)
    target_clean, _ = target.remove_statistical_outlier(nb_neighbors=20, std_ratio=2.0)
    print(f"  移除离群点后 - 源点云: {len(source_clean.points)} 个点, 目标点云: {len(target_clean.points)} 个点")
    
    # 执行高级精配准
    print("\n6. 执行高级精配准...")
    start_time = time.time()
    
    transformation = np.identity(4)
    
    # 阶段1：ICP配准
    print("  阶段1: 多尺度ICP配准")
    voxel_sizes = [0.05, 0.02, 0.01, 0.005]
    
    for i, voxel_size in enumerate(voxel_sizes):
        print(f"    子阶段{i+1}: 体素大小 {voxel_size:.3f}")
        
        # 下采样点云
        source_down = source_clean.voxel_down_sample(voxel_size)
        target_down = target_clean.voxel_down_sample(voxel_size)
        
        # 估计法向量
        source_down.estimate_normals(o3d.geometry.KDTreeSearchParamHybrid(radius=voxel_size * 2, max_nn=30))
        target_down.estimate_normals(o3d.geometry.KDTreeSearchParamHybrid(radius=voxel_size * 2, max_nn=30))
        
        # 执行ICP配准
        result = o3d.pipelines.registration.registration_icp(
            source_down, target_down, 
            max_correspondence_distance=voxel_size * 2,
            init=transformation,
            estimation_method=o3d.pipelines.registration.TransformationEstimationPointToPlane(),
            criteria=o3d.pipelines.registration.ICPConvergenceCriteria(max_iteration=100)
        )
        
        transformation = result.transformation
        print(f"      适应度: {result.fitness:.4f}, RMSE: {result.inlier_rmse:.4f}")
    
    # 阶段2：非线性优化
    print("  阶段2: 非线性优化")
    
    # 下采样点云用于优化
    source_opt = source_clean.voxel_down_sample(0.01)
    target_opt = target_clean.voxel_down_sample(0.01)
    
    optimized_transformation = nonlinear_optimization(
        np.asarray(source_opt.points),
        np.asarray(target_opt.points),
        transformation
    )
    
    transformation = optimized_transformation
    print(f"    非线性优化完成")
    
    end_time = time.time()
    print(f"高级精配准完成，耗时: {end_time - start_time:.2f} 秒")
    
    # 计算最终变换矩阵
    final_transformation = np.matmul(transformation, coarse_transformation)
    
    # 应用最终变换
    print("\n7. 应用最终变换...")
    source_final = o3d.geometry.PointCloud()
    source_final.points = o3d.utility.Vector3dVector(np.asarray(source.points))
    source_final.transform(final_transformation)
    
    # 计算最终指标
    print("\n8. 计算最终指标...")
    final_metrics = calculate_metrics(np.asarray(source_final.points), np.asarray(target.points))
    print(f"\n最终配准结果指标 (不裁剪):")
    print(f"  PV值: {final_metrics['pv_value']:.2f} 微米")
    print(f"  RMS: {final_metrics['rms']:.2f} 微米")
    
    # 检查是否达到目标精度
    target_pv = 90.0  # 微米
    target_rms = 20.0  # 微米
    
    if final_metrics['pv_value'] <= target_pv and final_metrics['rms'] <= target_rms:
        print(f"\n✅ 成功达到目标精度！")
        print(f"   PV值: {final_metrics['pv_value']:.2f} ≤ {target_pv} 微米")
        print(f"   RMS: {final_metrics['rms']:.2f} ≤ {target_rms} 微米")
    else:
        print(f"\n❌ 未达到目标精度")
        print(f"   PV值: {final_metrics['pv_value']:.2f} > {target_pv} 微米" if final_metrics['pv_value'] > target_pv else f"   PV值: {final_metrics['pv_value']:.2f} ≤ {target_pv} 微米 ✅")
        print(f"   RMS: {final_metrics['rms']:.2f} > {target_rms} 微米" if final_metrics['rms'] > target_rms else f"   RMS: {final_metrics['rms']:.2f} ≤ {target_rms} 微米 ✅")
        
        # 尝试不同的裁剪比例
        print(f"\n尝试不同的裁剪比例:")
        for trim_percentage in [0.01, 0.02, 0.03, 0.05, 0.10, 0.15, 0.20, 0.25, 0.30]:
            trimmed_metrics = calculate_metrics(
                np.asarray(source_final.points), 
                np.asarray(target.points),
                trim_percentage=trim_percentage
            )
            print(f"  裁剪 {trim_percentage*100:.1f}%: PV={trimmed_metrics['pv_value']:.2f}μm, RMS={trimmed_metrics['rms']:.2f}μm")
            
            if trimmed_metrics['pv_value'] <= target_pv and trimmed_metrics['rms'] <= target_rms:
                print(f"    ✅ 裁剪 {trim_percentage*100:.1f}% 后达到目标精度！")
                break
    
    # 保存结果
    print(f"\n9. 保存结果到 {results_dir}/...")
    
    # 保存最终变换后的点云
    o3d.io.write_point_cloud(
        os.path.join(results_dir, 'final_transformed_source.ply'),
        source_final
    )
    
    # 保存最终变换矩阵
    np.savetxt(
        os.path.join(results_dir, 'final_transformation.txt'),
        final_transformation
    )
    
    # 保存精配准变换矩阵
    np.savetxt(
        os.path.join(results_dir, 'fine_transformation.txt'),
        transformation
    )
    
    print("高级精配准完成!")

if __name__ == "__main__":
    main()
