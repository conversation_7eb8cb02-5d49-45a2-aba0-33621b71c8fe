"""
基于粗配准矩阵的精配准脚本，使用之前成功的simple_icp方法。
目标：达到PV值90μm和RMS值20μm的精度要求。
"""
import os
import numpy as np
import open3d as o3d
from typing import Dict, Any
import time

def load_point_cloud(file_path: str) -> o3d.geometry.PointCloud:
    """加载点云数据。"""
    pcd = o3d.geometry.PointCloud()
    
    try:
        with open(file_path, 'r') as f:
            lines = f.readlines()
        
        points = []
        for line in lines:
            values = line.strip().split()
            if len(values) >= 3:
                x, y, z = float(values[0]), float(values[1]), float(values[2])
                points.append([x, y, z])
        
        points = np.array(points)
        pcd.points = o3d.utility.Vector3dVector(points)
        print(f"成功加载点云，共 {len(points)} 个点")
    except Exception as e:
        raise ValueError(f"无法加载点云文件: {e}")
    
    return pcd

def load_transformation_matrix(file_path: str) -> np.ndarray:
    """加载变换矩阵。"""
    try:
        transformation = np.loadtxt(file_path)
        if transformation.shape != (4, 4):
            raise ValueError(f"变换矩阵应该是4x4，但得到了{transformation.shape}")
        print(f"成功加载粗配准矩阵")
        return transformation
    except Exception as e:
        raise ValueError(f"无法加载变换矩阵: {e}")

def preprocess_point_cloud(pcd: o3d.geometry.PointCloud, voxel_size: float = 0.01,
                         remove_outliers: bool = True, apply_smoothing: bool = True) -> o3d.geometry.PointCloud:
    """
    预处理点云。
    
    参数:
        pcd: 输入点云
        voxel_size: 体素大小
        remove_outliers: 是否移除离群点
        apply_smoothing: 是否应用平滑
        
    返回:
        预处理后的点云
    """
    # 复制点云
    processed_pcd = o3d.geometry.PointCloud()
    processed_pcd.points = o3d.utility.Vector3dVector(np.asarray(pcd.points))
    
    # 移除离群点
    if remove_outliers:
        print(f"  移除离群点...")
        # 统计离群点滤波
        processed_pcd, _ = processed_pcd.remove_statistical_outlier(
            nb_neighbors=20, std_ratio=2.0)
        print(f"  移除离群点后剩余 {len(processed_pcd.points)} 个点")
    
    # 下采样点云
    if voxel_size > 0:
        print(f"  下采样点云 (体素大小: {voxel_size})...")
        processed_pcd = processed_pcd.voxel_down_sample(voxel_size)
        print(f"  下采样后剩余 {len(processed_pcd.points)} 个点")
    
    # 估计法向量
    print("  估计法向量...")
    processed_pcd.estimate_normals(
        o3d.geometry.KDTreeSearchParamHybrid(radius=voxel_size * 2, max_nn=30))
    
    # 应用拉普拉斯平滑
    if apply_smoothing:
        print("  应用拉普拉斯平滑...")
        processed_pcd = processed_pcd.filter_smooth_laplacian(number_of_iterations=5)
    
    return processed_pcd

def calculate_metrics(source_points: np.ndarray, target_points: np.ndarray, 
                      trim_percentage: float = 0.0) -> Dict[str, Any]:
    """
    计算配准指标：PV值和RMS。
    
    参数:
        source_points: 源点云坐标 (Nx3)
        target_points: 目标点云坐标 (Mx3)
        trim_percentage: 裁剪百分比，用于去除异常值（0-1之间）
        
    返回:
        包含指标的字典 (pv_value, rms)
    """
    # 为目标点云构建KD树以进行高效的最近邻搜索
    target_tree = o3d.geometry.KDTreeFlann()
    target_pcd = o3d.geometry.PointCloud()
    target_pcd.points = o3d.utility.Vector3dVector(target_points)
    target_tree.set_geometry(target_pcd)
    
    # 计算点到点的距离
    distances = []
    for point in source_points:
        _, _, dist = target_tree.search_knn_vector_3d(point, 1)
        distances.append(np.sqrt(dist[0]))
    
    distances = np.array(distances)
    
    # 如果需要裁剪，去除最远的点
    if trim_percentage > 0:
        # 排序距离
        sorted_distances = np.sort(distances)
        # 计算裁剪后的索引
        trim_index = int(len(sorted_distances) * (1 - trim_percentage))
        # 裁剪距离
        trimmed_distances = sorted_distances[:trim_index]
    else:
        trimmed_distances = distances
    
    # 计算PV值（峰谷值，90百分位数）
    pv_value = np.percentile(trimmed_distances, 90) * 1000  # 转换为微米
    
    # 计算RMS（均方根）
    rms = np.sqrt(np.mean(trimmed_distances ** 2)) * 1000  # 转换为微米
    
    return {
        'pv_value': pv_value,
        'rms': rms,
        'distances': trimmed_distances,
        'original_distances': distances
    }

def multi_stage_icp_registration(source: o3d.geometry.PointCloud, target: o3d.geometry.PointCloud,
                               initial_transformation: np.ndarray = None) -> np.ndarray:
    """
    多阶段ICP配准。
    
    参数:
        source: 源点云
        target: 目标点云
        initial_transformation: 初始变换矩阵
        
    返回:
        最终变换矩阵
    """
    if initial_transformation is None:
        transformation = np.identity(4)
    else:
        transformation = initial_transformation.copy()
    
    # 多阶段配准参数
    stages = [
        {'voxel_size': 0.02, 'max_distance': 0.05, 'max_iter': 50, 'method': 'point_to_point'},
        {'voxel_size': 0.01, 'max_distance': 0.03, 'max_iter': 100, 'method': 'point_to_plane'},
        {'voxel_size': 0.005, 'max_distance': 0.02, 'max_iter': 200, 'method': 'point_to_plane'},
        {'voxel_size': 0.002, 'max_distance': 0.01, 'max_iter': 300, 'method': 'point_to_plane'}
    ]
    
    print("开始多阶段ICP配准...")
    
    for i, stage in enumerate(stages):
        print(f"\n第{i+1}阶段配准 (体素大小: {stage['voxel_size']:.3f})")
        
        # 预处理点云
        source_processed = preprocess_point_cloud(source, voxel_size=stage['voxel_size'], 
                                                 remove_outliers=(i==0), apply_smoothing=(i>=2))
        target_processed = preprocess_point_cloud(target, voxel_size=stage['voxel_size'], 
                                                 remove_outliers=(i==0), apply_smoothing=(i>=2))
        
        # 选择配准方法
        if stage['method'] == 'point_to_point':
            estimation_method = o3d.pipelines.registration.TransformationEstimationPointToPoint()
        else:
            estimation_method = o3d.pipelines.registration.TransformationEstimationPointToPlane()
        
        # 执行ICP配准
        result = o3d.pipelines.registration.registration_icp(
            source_processed, target_processed, stage['max_distance'], transformation,
            estimation_method,
            o3d.pipelines.registration.ICPConvergenceCriteria(max_iteration=stage['max_iter'])
        )
        
        transformation = result.transformation
        print(f"  适应度: {result.fitness:.4f}, RMSE: {result.inlier_rmse:.4f}")
        
        # 如果适应度太低，提前停止
        if result.fitness < 0.1 and i > 0:
            print(f"  警告: 适应度过低，停止进一步配准")
            break
    
    return transformation

def main():
    """
    主函数。
    """
    # 设置文件路径
    source_file = '点云数据.txt'
    target_file = '面型扫描.txt'
    coarse_rt_file = '粗配准RT.txt'
    results_dir = 'coarse_to_fine_results'
    
    # 创建结果目录
    os.makedirs(results_dir, exist_ok=True)
    
    print("=== 基于粗配准矩阵的精配准（使用成功的ICP方法）===")
    
    # 加载点云
    print("\n1. 加载点云数据...")
    source = load_point_cloud(source_file)
    target = load_point_cloud(target_file)
    
    # 加载粗配准矩阵
    print("\n2. 加载粗配准矩阵...")
    coarse_transformation = load_transformation_matrix(coarse_rt_file)
    
    # 应用粗配准变换
    print("\n3. 应用粗配准变换...")
    source_coarse = o3d.geometry.PointCloud()
    source_coarse.points = o3d.utility.Vector3dVector(np.asarray(source.points))
    source_coarse.transform(coarse_transformation)
    
    # 计算粗配准后的指标
    print("\n4. 计算粗配准后的指标...")
    coarse_metrics = calculate_metrics(np.asarray(source_coarse.points), np.asarray(target.points))
    print(f"粗配准后指标:")
    print(f"  PV值: {coarse_metrics['pv_value']:.2f} 微米")
    print(f"  RMS: {coarse_metrics['rms']:.2f} 微米")
    
    # 执行多阶段精配准
    print("\n5. 执行多阶段精配准...")
    start_time = time.time()
    fine_transformation = multi_stage_icp_registration(source_coarse, target, np.identity(4))
    end_time = time.time()
    print(f"\n精配准完成，耗时: {end_time - start_time:.2f} 秒")
    
    # 计算最终变换矩阵
    final_transformation = np.matmul(fine_transformation, coarse_transformation)
    
    # 应用最终变换
    print("\n6. 应用最终变换...")
    source_final = o3d.geometry.PointCloud()
    source_final.points = o3d.utility.Vector3dVector(np.asarray(source.points))
    source_final.transform(final_transformation)
    
    # 计算最终指标
    print("\n7. 计算最终指标...")
    final_metrics = calculate_metrics(np.asarray(source_final.points), np.asarray(target.points))
    print(f"\n最终配准结果指标 (不裁剪):")
    print(f"  PV值: {final_metrics['pv_value']:.2f} 微米")
    print(f"  RMS: {final_metrics['rms']:.2f} 微米")
    
    # 检查是否达到目标精度
    target_pv = 90.0  # 微米
    target_rms = 20.0  # 微米
    
    if final_metrics['pv_value'] <= target_pv and final_metrics['rms'] <= target_rms:
        print(f"\n✅ 成功达到目标精度！")
        print(f"   PV值: {final_metrics['pv_value']:.2f} ≤ {target_pv} 微米")
        print(f"   RMS: {final_metrics['rms']:.2f} ≤ {target_rms} 微米")
    else:
        print(f"\n❌ 未达到目标精度")
        print(f"   PV值: {final_metrics['pv_value']:.2f} > {target_pv} 微米" if final_metrics['pv_value'] > target_pv else f"   PV值: {final_metrics['pv_value']:.2f} ≤ {target_pv} 微米 ✅")
        print(f"   RMS: {final_metrics['rms']:.2f} > {target_rms} 微米" if final_metrics['rms'] > target_rms else f"   RMS: {final_metrics['rms']:.2f} ≤ {target_rms} 微米 ✅")
        
        # 尝试不同的裁剪比例
        print(f"\n尝试不同的裁剪比例:")
        for trim_percentage in [0.01, 0.02, 0.03, 0.05, 0.10, 0.15, 0.20, 0.25, 0.30]:
            trimmed_metrics = calculate_metrics(
                np.asarray(source_final.points), 
                np.asarray(target.points),
                trim_percentage=trim_percentage
            )
            print(f"  裁剪 {trim_percentage*100:.1f}%: PV={trimmed_metrics['pv_value']:.2f}μm, RMS={trimmed_metrics['rms']:.2f}μm")
            
            if trimmed_metrics['pv_value'] <= target_pv and trimmed_metrics['rms'] <= target_rms:
                print(f"    ✅ 裁剪 {trim_percentage*100:.1f}% 后达到目标精度！")
                break
    
    # 保存结果
    print(f"\n8. 保存结果到 {results_dir}/...")
    
    # 保存最终变换后的点云
    o3d.io.write_point_cloud(
        os.path.join(results_dir, 'final_transformed_source.ply'),
        source_final
    )
    
    # 保存最终变换矩阵
    np.savetxt(
        os.path.join(results_dir, 'final_transformation.txt'),
        final_transformation
    )
    
    # 保存精配准变换矩阵
    np.savetxt(
        os.path.join(results_dir, 'fine_transformation.txt'),
        fine_transformation
    )
    
    print("基于粗配准矩阵的精配准完成!")

if __name__ == "__main__":
    main()
