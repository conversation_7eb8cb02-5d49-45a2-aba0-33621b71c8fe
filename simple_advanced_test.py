"""
简单的高级配准算法测试。
专注于几种最有效的方法。
"""
import os
import numpy as np
import open3d as o3d
import time

def load_point_cloud(file_path: str) -> o3d.geometry.PointCloud:
    """加载点云数据。"""
    pcd = o3d.geometry.PointCloud()
    
    with open(file_path, 'r') as f:
        lines = f.readlines()
    
    points = []
    for line in lines:
        values = line.strip().split()
        if len(values) >= 3:
            x, y, z = float(values[0]), float(values[1]), float(values[2])
            points.append([x, y, z])
    
    points = np.array(points)
    pcd.points = o3d.utility.Vector3dVector(points)
    print(f"成功加载点云，共 {len(points)} 个点")
    return pcd

def calculate_metrics(source_points: np.ndarray, target_points: np.ndarray):
    """计算配准指标。"""
    target_tree = o3d.geometry.KDTreeFlann()
    target_pcd = o3d.geometry.PointCloud()
    target_pcd.points = o3d.utility.Vector3dVector(target_points)
    target_tree.set_geometry(target_pcd)
    
    distances = []
    for point in source_points:
        _, _, dist = target_tree.search_knn_vector_3d(point, 1)
        distances.append(np.sqrt(dist[0]))
    
    distances = np.array(distances)
    pv_value = np.percentile(distances, 90) * 1000  # 转换为微米
    rms = np.sqrt(np.mean(distances ** 2)) * 1000  # 转换为微米
    
    return {'pv_value': pv_value, 'rms': rms}

def enhanced_icp(source, target, initial_transformation, max_iterations=5):
    """增强的ICP配准。"""
    print("  执行增强ICP配准...")
    
    transformation = initial_transformation.copy()
    
    # 多阶段配准
    stages = [
        {'voxel_size': 0.02, 'max_distance': 0.05, 'max_iter': 50},
        {'voxel_size': 0.01, 'max_distance': 0.02, 'max_iter': 100},
        {'voxel_size': 0.005, 'max_distance': 0.01, 'max_iter': 150},
        {'voxel_size': 0.002, 'max_distance': 0.005, 'max_iter': 200},
    ]
    
    for i, stage in enumerate(stages):
        print(f"    阶段{i+1}: 体素大小={stage['voxel_size']:.3f}")
        
        # 下采样
        source_down = source.voxel_down_sample(stage['voxel_size'])
        target_down = target.voxel_down_sample(stage['voxel_size'])
        
        # 估计法向量
        source_down.estimate_normals(
            o3d.geometry.KDTreeSearchParamHybrid(radius=stage['voxel_size'] * 2, max_nn=30))
        target_down.estimate_normals(
            o3d.geometry.KDTreeSearchParamHybrid(radius=stage['voxel_size'] * 2, max_nn=30))
        
        # 执行ICP
        result = o3d.pipelines.registration.registration_icp(
            source_down, target_down,
            max_correspondence_distance=stage['max_distance'],
            init=transformation,
            estimation_method=o3d.pipelines.registration.TransformationEstimationPointToPlane(),
            criteria=o3d.pipelines.registration.ICPConvergenceCriteria(max_iteration=stage['max_iter'])
        )
        
        print(f"      适应度: {result.fitness:.4f}, RMSE: {result.inlier_rmse:.4f}")
        
        if result.fitness > 0.01:
            transformation = result.transformation
    
    return transformation

def robust_icp(source, target, initial_transformation):
    """鲁棒ICP配准。"""
    print("  执行鲁棒ICP配准...")
    
    voxel_size = 0.005
    source_down = source.voxel_down_sample(voxel_size)
    target_down = target.voxel_down_sample(voxel_size)
    
    source_down.estimate_normals(
        o3d.geometry.KDTreeSearchParamHybrid(radius=voxel_size * 2, max_nn=30))
    target_down.estimate_normals(
        o3d.geometry.KDTreeSearchParamHybrid(radius=voxel_size * 2, max_nn=30))
    
    # 尝试使用鲁棒损失函数
    try:
        result = o3d.pipelines.registration.registration_icp(
            source_down, target_down,
            max_correspondence_distance=voxel_size * 2,
            init=initial_transformation,
            estimation_method=o3d.pipelines.registration.TransformationEstimationPointToPlane(
                o3d.pipelines.registration.RobustKernel(o3d.pipelines.registration.RobustKernelMethod.Huber, 0.01)
            ),
            criteria=o3d.pipelines.registration.ICPConvergenceCriteria(max_iteration=200)
        )
        print(f"    鲁棒ICP结果: 适应度={result.fitness:.4f}, RMSE={result.inlier_rmse:.4f}")
        return result.transformation
    except:
        # 如果鲁棒ICP不可用，使用标准ICP
        result = o3d.pipelines.registration.registration_icp(
            source_down, target_down,
            max_correspondence_distance=voxel_size * 2,
            init=initial_transformation,
            estimation_method=o3d.pipelines.registration.TransformationEstimationPointToPlane(),
            criteria=o3d.pipelines.registration.ICPConvergenceCriteria(max_iteration=200)
        )
        print(f"    标准ICP结果: 适应度={result.fitness:.4f}, RMSE={result.inlier_rmse:.4f}")
        return result.transformation

def iterative_refinement(source, target, initial_transformation, iterations=3):
    """迭代细化配准。"""
    print("  执行迭代细化配准...")
    
    transformation = initial_transformation.copy()
    
    for i in range(iterations):
        print(f"    迭代 {i+1}:")
        
        # 应用当前变换
        source_transformed = o3d.geometry.PointCloud()
        source_transformed.points = o3d.utility.Vector3dVector(np.asarray(source.points))
        source_transformed.transform(transformation)
        
        # 计算当前误差
        current_metrics = calculate_metrics(np.asarray(source_transformed.points), np.asarray(target.points))
        print(f"      当前误差: PV={current_metrics['pv_value']:.2f}μm, RMS={current_metrics['rms']:.2f}μm")
        
        # 根据误差调整参数
        error_mm = current_metrics['rms'] / 1000.0
        
        if error_mm < 0.05:
            voxel_size = 0.001
            max_distance = error_mm * 0.8
        elif error_mm < 0.1:
            voxel_size = 0.002
            max_distance = error_mm * 0.9
        else:
            voxel_size = 0.005
            max_distance = error_mm * 1.0
        
        # 预处理
        source_processed = source_transformed.voxel_down_sample(voxel_size)
        target_processed = target.voxel_down_sample(voxel_size)
        
        source_processed.estimate_normals(
            o3d.geometry.KDTreeSearchParamHybrid(radius=voxel_size * 2, max_nn=30))
        target_processed.estimate_normals(
            o3d.geometry.KDTreeSearchParamHybrid(radius=voxel_size * 2, max_nn=30))
        
        # 执行ICP
        result = o3d.pipelines.registration.registration_icp(
            source_processed, target_processed,
            max_correspondence_distance=max_distance,
            init=np.identity(4),
            estimation_method=o3d.pipelines.registration.TransformationEstimationPointToPlane(),
            criteria=o3d.pipelines.registration.ICPConvergenceCriteria(max_iteration=200)
        )
        
        print(f"      ICP结果: 适应度={result.fitness:.4f}, RMSE={result.inlier_rmse:.4f}")
        
        if result.fitness > 0.01:
            new_transformation = np.matmul(result.transformation, transformation)
            
            # 验证改进
            source_new = o3d.geometry.PointCloud()
            source_new.points = o3d.utility.Vector3dVector(np.asarray(source.points))
            source_new.transform(new_transformation)
            
            new_metrics = calculate_metrics(np.asarray(source_new.points), np.asarray(target.points))
            
            if new_metrics['rms'] < current_metrics['rms']:
                transformation = new_transformation
                print(f"      ✅ 改进: 新RMS={new_metrics['rms']:.2f}μm")
                
                improvement = (current_metrics['rms'] - new_metrics['rms']) / current_metrics['rms']
                if improvement < 0.01:
                    print(f"      改进幅度很小，停止迭代")
                    break
            else:
                print(f"      ❌ 无改进，停止迭代")
                break
        else:
            print(f"      ❌ 适应度过低，停止迭代")
            break
    
    return transformation

def main():
    """主函数。"""
    source_file = '点云数据.txt'
    target_file = '面型扫描.txt'
    coarse_rt_file = '粗配准RT.txt'
    results_dir = 'simple_advanced_results'
    
    os.makedirs(results_dir, exist_ok=True)
    
    print("=== 简单高级配准算法测试 ===")
    
    # 1. 加载数据
    print("\n1. 加载数据...")
    source = load_point_cloud(source_file)
    target = load_point_cloud(target_file)
    coarse_transformation = np.loadtxt(coarse_rt_file)
    
    # 2. 应用粗配准
    print("\n2. 应用粗配准...")
    source_coarse = o3d.geometry.PointCloud()
    source_coarse.points = o3d.utility.Vector3dVector(np.asarray(source.points))
    source_coarse.transform(coarse_transformation)
    
    coarse_metrics = calculate_metrics(np.asarray(source_coarse.points), np.asarray(target.points))
    print(f"粗配准后: PV={coarse_metrics['pv_value']:.2f}μm, RMS={coarse_metrics['rms']:.2f}μm")
    
    # 3. 测试算法
    algorithms = []
    
    # 算法1: 增强ICP
    print("\n3. 测试增强ICP...")
    start_time = time.time()
    try:
        enhanced_transformation = enhanced_icp(source_coarse, target, np.identity(4))
        enhanced_final = np.matmul(enhanced_transformation, coarse_transformation)
        
        source_enhanced = o3d.geometry.PointCloud()
        source_enhanced.points = o3d.utility.Vector3dVector(np.asarray(source.points))
        source_enhanced.transform(enhanced_final)
        
        enhanced_metrics = calculate_metrics(np.asarray(source_enhanced.points), np.asarray(target.points))
        algorithms.append({
            'name': '增强ICP',
            'transformation': enhanced_final,
            'metrics': enhanced_metrics,
            'time': time.time() - start_time
        })
        print(f"结果: PV={enhanced_metrics['pv_value']:.2f}μm, RMS={enhanced_metrics['rms']:.2f}μm")
    except Exception as e:
        print(f"增强ICP失败: {e}")
    
    # 算法2: 鲁棒ICP
    print("\n4. 测试鲁棒ICP...")
    start_time = time.time()
    try:
        robust_transformation = robust_icp(source_coarse, target, np.identity(4))
        robust_final = np.matmul(robust_transformation, coarse_transformation)
        
        source_robust = o3d.geometry.PointCloud()
        source_robust.points = o3d.utility.Vector3dVector(np.asarray(source.points))
        source_robust.transform(robust_final)
        
        robust_metrics = calculate_metrics(np.asarray(source_robust.points), np.asarray(target.points))
        algorithms.append({
            'name': '鲁棒ICP',
            'transformation': robust_final,
            'metrics': robust_metrics,
            'time': time.time() - start_time
        })
        print(f"结果: PV={robust_metrics['pv_value']:.2f}μm, RMS={robust_metrics['rms']:.2f}μm")
    except Exception as e:
        print(f"鲁棒ICP失败: {e}")
    
    # 算法3: 迭代细化
    print("\n5. 测试迭代细化...")
    start_time = time.time()
    try:
        iterative_transformation = iterative_refinement(source_coarse, target, np.identity(4))
        iterative_final = np.matmul(iterative_transformation, coarse_transformation)
        
        source_iterative = o3d.geometry.PointCloud()
        source_iterative.points = o3d.utility.Vector3dVector(np.asarray(source.points))
        source_iterative.transform(iterative_final)
        
        iterative_metrics = calculate_metrics(np.asarray(source_iterative.points), np.asarray(target.points))
        algorithms.append({
            'name': '迭代细化',
            'transformation': iterative_final,
            'metrics': iterative_metrics,
            'time': time.time() - start_time
        })
        print(f"结果: PV={iterative_metrics['pv_value']:.2f}μm, RMS={iterative_metrics['rms']:.2f}μm")
    except Exception as e:
        print(f"迭代细化失败: {e}")
    
    # 4. 结果对比
    if algorithms:
        print("\n6. 结果对比:")
        print("=" * 70)
        print(f"{'算法':<12} {'PV(μm)':<10} {'RMS(μm)':<10} {'时间(s)':<8} {'改进'}")
        print("-" * 70)
        
        best_algorithm = None
        best_score = float('inf')
        
        for alg in algorithms:
            pv_imp = (coarse_metrics['pv_value'] - alg['metrics']['pv_value']) / coarse_metrics['pv_value'] * 100
            rms_imp = (coarse_metrics['rms'] - alg['metrics']['rms']) / coarse_metrics['rms'] * 100
            score = alg['metrics']['pv_value'] * 0.5 + alg['metrics']['rms'] * 0.5
            
            print(f"{alg['name']:<12} {alg['metrics']['pv_value']:<10.2f} {alg['metrics']['rms']:<10.2f} {alg['time']:<8.2f} PV:{pv_imp:+.1f}% RMS:{rms_imp:+.1f}%")
            
            if score < best_score:
                best_score = score
                best_algorithm = alg
        
        print(f"\n最佳算法: {best_algorithm['name']}")
        print(f"PV值: {best_algorithm['metrics']['pv_value']:.2f} 微米")
        print(f"RMS值: {best_algorithm['metrics']['rms']:.2f} 微米")
        
        # 检查目标精度
        target_pv = 90.0
        target_rms = 20.0
        
        if best_algorithm['metrics']['pv_value'] <= target_pv and best_algorithm['metrics']['rms'] <= target_rms:
            print(f"\n🎉 达到目标精度！")
        else:
            print(f"\n❌ 未达到目标精度 (PV≤{target_pv}μm, RMS≤{target_rms}μm)")
        
        # 保存最佳结果
        np.savetxt(os.path.join(results_dir, 'best_transformation.txt'), best_algorithm['transformation'])
        
        source_best = o3d.geometry.PointCloud()
        source_best.points = o3d.utility.Vector3dVector(np.asarray(source.points))
        source_best.transform(best_algorithm['transformation'])
        o3d.io.write_point_cloud(os.path.join(results_dir, 'best_result.ply'), source_best)
        
        print(f"\n结果已保存到 {results_dir}/")
    
    print("测试完成!")

if __name__ == "__main__":
    main()
