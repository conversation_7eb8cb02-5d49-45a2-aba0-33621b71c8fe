"""
自适应精密配准算法。
使用多种策略组合来达到高精度配准要求。
目标：达到PV值90μm和RMS值20μm的精度要求。
"""
import os
import numpy as np
import open3d as o3d
from typing import Dict, Any, Tuple, List
import time
from scipy.optimize import minimize
from scipy.spatial.distance import cdist

def load_point_cloud(file_path: str) -> o3d.geometry.PointCloud:
    """加载点云数据。"""
    pcd = o3d.geometry.PointCloud()
    
    try:
        with open(file_path, 'r') as f:
            lines = f.readlines()
        
        points = []
        for line in lines:
            values = line.strip().split()
            if len(values) >= 3:
                x, y, z = float(values[0]), float(values[1]), float(values[2])
                points.append([x, y, z])
        
        points = np.array(points)
        pcd.points = o3d.utility.Vector3dVector(points)
        print(f"成功加载点云，共 {len(points)} 个点")
    except Exception as e:
        raise ValueError(f"无法加载点云文件: {e}")
    
    return pcd

def calculate_metrics(source_points: np.ndarray, target_points: np.ndarray, 
                      trim_percentage: float = 0.0) -> Dict[str, Any]:
    """计算配准指标：PV值和RMS。"""
    target_tree = o3d.geometry.KDTreeFlann()
    target_pcd = o3d.geometry.PointCloud()
    target_pcd.points = o3d.utility.Vector3dVector(target_points)
    target_tree.set_geometry(target_pcd)
    
    distances = []
    for point in source_points:
        _, _, dist = target_tree.search_knn_vector_3d(point, 1)
        distances.append(np.sqrt(dist[0]))
    
    distances = np.array(distances)
    
    if trim_percentage > 0:
        sorted_distances = np.sort(distances)
        trim_index = int(len(sorted_distances) * (1 - trim_percentage))
        trimmed_distances = sorted_distances[:trim_index]
    else:
        trimmed_distances = distances
    
    pv_value = np.percentile(trimmed_distances, 90) * 1000  # 转换为微米
    rms = np.sqrt(np.mean(trimmed_distances ** 2)) * 1000  # 转换为微米
    
    return {
        'pv_value': pv_value,
        'rms': rms,
        'distances': trimmed_distances,
        'original_distances': distances
    }

def smart_downsampling(pcd: o3d.geometry.PointCloud, target_points: int = 5000) -> o3d.geometry.PointCloud:
    """智能下采样：保持几何特征的同时减少点数。"""
    points = np.asarray(pcd.points)
    
    if len(points) <= target_points:
        return pcd
    
    # 计算点的曲率（基于局部邻域）
    pcd_temp = o3d.geometry.PointCloud()
    pcd_temp.points = o3d.utility.Vector3dVector(points)
    pcd_temp.estimate_normals(o3d.geometry.KDTreeSearchParamHybrid(radius=0.01, max_nn=30))
    
    normals = np.asarray(pcd_temp.normals)
    
    # 计算每个点的重要性（基于法向量变化）
    tree = o3d.geometry.KDTreeFlann()
    tree.set_geometry(pcd_temp)
    
    importance_scores = []
    for i, point in enumerate(points):
        _, indices, _ = tree.search_radius_vector_3d(point, 0.01)
        if len(indices) > 1:
            neighbor_normals = normals[indices]
            normal_variance = np.var(neighbor_normals, axis=0).sum()
            importance_scores.append(normal_variance)
        else:
            importance_scores.append(0.0)
    
    importance_scores = np.array(importance_scores)
    
    # 选择最重要的点
    top_indices = np.argsort(importance_scores)[-target_points:]
    
    downsampled_pcd = o3d.geometry.PointCloud()
    downsampled_pcd.points = o3d.utility.Vector3dVector(points[top_indices])
    
    return downsampled_pcd

def robust_correspondence_estimation(source: o3d.geometry.PointCloud, target: o3d.geometry.PointCloud,
                                   max_distance: float = 0.01) -> Tuple[np.ndarray, np.ndarray]:
    """鲁棒对应关系估计。"""
    source_points = np.asarray(source.points)
    target_points = np.asarray(target.points)
    
    # 构建目标点云的KD树
    target_tree = o3d.geometry.KDTreeFlann()
    target_tree.set_geometry(target)
    
    source_correspondences = []
    target_correspondences = []
    
    for i, point in enumerate(source_points):
        _, indices, distances = target_tree.search_knn_vector_3d(point, 1)
        
        if len(distances) > 0 and np.sqrt(distances[0]) < max_distance:
            source_correspondences.append(i)
            target_correspondences.append(indices[0])
    
    return np.array(source_correspondences), np.array(target_correspondences)

def weighted_icp(source: o3d.geometry.PointCloud, target: o3d.geometry.PointCloud,
                initial_transformation: np.ndarray, max_iterations: int = 100) -> np.ndarray:
    """加权ICP配准。"""
    print("  执行加权ICP配准...")
    
    transformation = initial_transformation.copy()
    
    for iteration in range(max_iterations):
        # 应用当前变换
        source_transformed = o3d.geometry.PointCloud()
        source_transformed.points = o3d.utility.Vector3dVector(np.asarray(source.points))
        source_transformed.transform(transformation)
        
        # 估计对应关系
        source_corr, target_corr = robust_correspondence_estimation(
            source_transformed, target, max_distance=0.01
        )
        
        if len(source_corr) < 100:
            print(f"    迭代 {iteration+1}: 对应点太少 ({len(source_corr)})，停止")
            break
        
        # 获取对应点
        source_points = np.asarray(source_transformed.points)[source_corr]
        target_points = np.asarray(target.points)[target_corr]
        
        # 计算距离权重
        distances = np.linalg.norm(source_points - target_points, axis=1)
        weights = np.exp(-distances / 0.005)  # 距离越近权重越大
        weights = weights / np.sum(weights)
        
        # 加权最小二乘求解
        source_centroid = np.average(source_points, weights=weights, axis=0)
        target_centroid = np.average(target_points, weights=weights, axis=0)
        
        source_centered = source_points - source_centroid
        target_centered = target_points - target_centroid
        
        # 加权协方差矩阵
        H = np.zeros((3, 3))
        for i in range(len(source_points)):
            H += weights[i] * np.outer(source_centered[i], target_centered[i])
        
        # SVD求解旋转
        U, _, Vt = np.linalg.svd(H)
        R = Vt.T @ U.T
        
        # 确保是正确的旋转矩阵
        if np.linalg.det(R) < 0:
            Vt[-1, :] *= -1
            R = Vt.T @ U.T
        
        t = target_centroid - R @ source_centroid
        
        # 构建变换矩阵
        delta_transformation = np.eye(4)
        delta_transformation[:3, :3] = R
        delta_transformation[:3, 3] = t
        
        # 更新变换
        new_transformation = delta_transformation @ transformation
        
        # 检查收敛
        transformation_change = np.linalg.norm(delta_transformation - np.eye(4))
        
        if iteration % 10 == 0:
            print(f"    迭代 {iteration+1}: 对应点={len(source_corr)}, 变换变化={transformation_change:.6f}")
        
        if transformation_change < 1e-6:
            print(f"    收敛于迭代 {iteration+1}")
            break
        
        transformation = new_transformation
    
    return transformation

def multi_scale_registration(source: o3d.geometry.PointCloud, target: o3d.geometry.PointCloud,
                           initial_transformation: np.ndarray) -> np.ndarray:
    """多尺度配准。"""
    print("  执行多尺度配准...")
    
    transformation = initial_transformation.copy()
    
    # 多尺度参数
    scales = [
        {'voxel_size': 0.02, 'max_distance': 0.05, 'max_iter': 50, 'name': '粗尺度'},
        {'voxel_size': 0.01, 'max_distance': 0.02, 'max_iter': 100, 'name': '中尺度'},
        {'voxel_size': 0.005, 'max_distance': 0.01, 'max_iter': 150, 'name': '细尺度'},
        {'voxel_size': 0.002, 'max_distance': 0.005, 'max_iter': 200, 'name': '超细尺度'},
    ]
    
    for i, scale in enumerate(scales):
        print(f"    {scale['name']}: 体素大小={scale['voxel_size']:.3f}")
        
        # 下采样
        source_down = source.voxel_down_sample(scale['voxel_size'])
        target_down = target.voxel_down_sample(scale['voxel_size'])
        
        # 智能下采样以控制计算量
        if len(source_down.points) > 10000:
            source_down = smart_downsampling(source_down, 10000)
        if len(target_down.points) > 15000:
            target_down = smart_downsampling(target_down, 15000)
        
        print(f"      处理点数: 源={len(source_down.points)}, 目标={len(target_down.points)}")
        
        # 估计法向量
        source_down.estimate_normals(
            o3d.geometry.KDTreeSearchParamHybrid(radius=scale['voxel_size'] * 2, max_nn=30))
        target_down.estimate_normals(
            o3d.geometry.KDTreeSearchParamHybrid(radius=scale['voxel_size'] * 2, max_nn=30))
        
        # 应用当前变换
        source_transformed = o3d.geometry.PointCloud()
        source_transformed.points = o3d.utility.Vector3dVector(np.asarray(source_down.points))
        source_transformed.normals = o3d.utility.Vector3dVector(np.asarray(source_down.normals))
        source_transformed.transform(transformation)
        
        # 执行加权ICP
        delta_transformation = weighted_icp(
            source_transformed, target_down, np.identity(4), max_iterations=scale['max_iter']
        )
        
        # 更新总变换
        transformation = np.matmul(delta_transformation, transformation)
        
        # 计算当前质量
        source_temp = o3d.geometry.PointCloud()
        source_temp.points = o3d.utility.Vector3dVector(np.asarray(source.points))
        source_temp.transform(transformation)
        
        temp_metrics = calculate_metrics(np.asarray(source_temp.points), np.asarray(target.points))
        print(f"      当前质量: PV={temp_metrics['pv_value']:.2f}μm, RMS={temp_metrics['rms']:.2f}μm")
        
        # 如果已经达到很好的精度，可以提前停止
        if temp_metrics['rms'] < 50.0:  # 50微米
            print(f"      达到中等精度，继续细化...")
    
    return transformation

def local_optimization(source_points: np.ndarray, target_points: np.ndarray,
                      initial_transformation: np.ndarray) -> np.ndarray:
    """局部优化：使用数值优化方法进一步细化配准。"""
    print("  执行局部优化...")
    
    # 下采样以提高速度
    if len(source_points) > 5000:
        indices = np.random.choice(len(source_points), 5000, replace=False)
        source_sample = source_points[indices]
    else:
        source_sample = source_points
    
    print(f"    使用 {len(source_sample)} 个源点进行优化")
    
    # 构建目标点云的KD树
    target_tree = o3d.geometry.KDTreeFlann()
    target_pcd = o3d.geometry.PointCloud()
    target_pcd.points = o3d.utility.Vector3dVector(target_points)
    target_tree.set_geometry(target_pcd)
    
    def transform_points(points, params):
        """根据参数变换点云。"""
        # 参数: [tx, ty, tz, rx, ry, rz] (平移 + 小角度旋转)
        tx, ty, tz, rx, ry, rz = params
        
        # 小角度近似
        R = np.array([
            [1, -rz, ry],
            [rz, 1, -rx],
            [-ry, rx, 1]
        ])
        
        t = np.array([tx, ty, tz])
        
        return (R @ points.T).T + t
    
    def objective_function(params):
        """目标函数：最小化点到点距离。"""
        try:
            transformed_source = transform_points(source_sample, params)
            
            distances = []
            for point in transformed_source:
                _, _, dist = target_tree.search_knn_vector_3d(point, 1)
                if len(dist) > 0:
                    distances.append(np.sqrt(dist[0]))
            
            if len(distances) == 0:
                return 1e6
            
            distances = np.array(distances)
            
            # 使用鲁棒损失函数
            huber_delta = 0.005
            residuals = distances
            huber_loss = np.where(residuals <= huber_delta,
                                0.5 * residuals**2,
                                huber_delta * (residuals - 0.5 * huber_delta))
            
            return np.mean(huber_loss)
        except Exception as e:
            return 1e6
    
    # 初始参数（小的扰动）
    initial_params = np.array([0.0, 0.0, 0.0, 0.0, 0.0, 0.0])
    
    # 执行优化
    try:
        print("    开始数值优化...")
        result = minimize(objective_function, initial_params, 
                         method='L-BFGS-B',
                         bounds=[(-0.01, 0.01)] * 6,  # 限制搜索范围
                         options={'maxiter': 100})
        
        if result.success:
            tx, ty, tz, rx, ry, rz = result.x
            
            # 构建优化变换矩阵
            R = np.array([
                [1, -rz, ry],
                [rz, 1, -rx],
                [-ry, rx, 1]
            ])
            
            optimization_transformation = np.eye(4)
            optimization_transformation[:3, :3] = R
            optimization_transformation[:3, 3] = [tx, ty, tz]
            
            print(f"    优化成功: 目标值={result.fun:.6f}")
            print(f"    优化参数: 平移=[{tx:.6f}, {ty:.6f}, {tz:.6f}], "
                  f"旋转=[{rx:.6f}, {ry:.6f}, {rz:.6f}]")
            
            return np.matmul(optimization_transformation, initial_transformation)
        else:
            print(f"    优化失败: {result.message}")
            return initial_transformation
    except Exception as e:
        print(f"    优化异常: {e}")
        return initial_transformation

def main():
    """主函数。"""
    source_file = '点云数据.txt'
    target_file = '面型扫描.txt'
    coarse_rt_file = '粗配准RT.txt'
    results_dir = 'adaptive_precision_results'
    
    os.makedirs(results_dir, exist_ok=True)
    
    print("=== 自适应精密配准算法 ===")
    
    # 1. 加载数据
    print("\n1. 加载数据...")
    source = load_point_cloud(source_file)
    target = load_point_cloud(target_file)
    coarse_transformation = np.loadtxt(coarse_rt_file)
    
    # 2. 应用粗配准
    print("\n2. 应用粗配准...")
    source_coarse = o3d.geometry.PointCloud()
    source_coarse.points = o3d.utility.Vector3dVector(np.asarray(source.points))
    source_coarse.transform(coarse_transformation)
    
    coarse_metrics = calculate_metrics(np.asarray(source_coarse.points), np.asarray(target.points))
    print(f"粗配准后: PV={coarse_metrics['pv_value']:.2f}μm, RMS={coarse_metrics['rms']:.2f}μm")
    
    # 3. 多尺度配准
    print("\n3. 多尺度配准...")
    start_time = time.time()
    multiscale_transformation = multi_scale_registration(source_coarse, target, np.identity(4))
    multiscale_time = time.time() - start_time
    
    multiscale_final = np.matmul(multiscale_transformation, coarse_transformation)
    
    source_multiscale = o3d.geometry.PointCloud()
    source_multiscale.points = o3d.utility.Vector3dVector(np.asarray(source.points))
    source_multiscale.transform(multiscale_final)
    
    multiscale_metrics = calculate_metrics(np.asarray(source_multiscale.points), np.asarray(target.points))
    print(f"多尺度配准结果: PV={multiscale_metrics['pv_value']:.2f}μm, RMS={multiscale_metrics['rms']:.2f}μm")
    
    # 4. 局部优化
    print("\n4. 局部优化...")
    start_time = time.time()
    
    optimization_transformation = local_optimization(
        np.asarray(source_multiscale.points),
        np.asarray(target.points),
        np.identity(4)
    )
    
    optimization_time = time.time() - start_time
    
    final_transformation = np.matmul(optimization_transformation, multiscale_final)
    
    # 5. 计算最终结果
    print("\n5. 计算最终结果...")
    source_final = o3d.geometry.PointCloud()
    source_final.points = o3d.utility.Vector3dVector(np.asarray(source.points))
    source_final.transform(final_transformation)
    
    final_metrics = calculate_metrics(np.asarray(source_final.points), np.asarray(target.points))
    
    # 6. 结果总结
    print("\n6. 结果总结:")
    print("=" * 70)
    print(f"{'阶段':<20} {'PV值(μm)':<12} {'RMS值(μm)':<12} {'耗时(s)':<10} {'改进'}")
    print("-" * 70)
    
    stages = [
        ('粗配准', coarse_metrics, 0),
        ('多尺度配准', multiscale_metrics, multiscale_time),
        ('局部优化', final_metrics, optimization_time)
    ]
    
    for stage_name, metrics, stage_time in stages:
        if stage_name == '粗配准':
            improvement = "基线"
        else:
            pv_imp = (coarse_metrics['pv_value'] - metrics['pv_value']) / coarse_metrics['pv_value'] * 100
            rms_imp = (coarse_metrics['rms'] - metrics['rms']) / coarse_metrics['rms'] * 100
            improvement = f"PV:{pv_imp:+.1f}% RMS:{rms_imp:+.1f}%"
        
        print(f"{stage_name:<20} {metrics['pv_value']:<12.2f} {metrics['rms']:<12.2f} {stage_time:<10.2f} {improvement}")
    
    # 检查目标精度
    target_pv = 90.0
    target_rms = 20.0
    
    print(f"\n目标精度: PV≤{target_pv}μm, RMS≤{target_rms}μm")
    
    if final_metrics['pv_value'] <= target_pv and final_metrics['rms'] <= target_rms:
        print(f"🎉 成功达到目标精度！")
    else:
        print(f"❌ 未达到目标精度")
        print(f"   PV值: {final_metrics['pv_value']:.2f} {'≤' if final_metrics['pv_value'] <= target_pv else '>'} {target_pv}μm")
        print(f"   RMS值: {final_metrics['rms']:.2f} {'≤' if final_metrics['rms'] <= target_rms else '>'} {target_rms}μm")
        
        # 测试裁剪效果
        print(f"\n测试边缘裁剪效果:")
        for trim_percentage in [0.05, 0.1, 0.15, 0.2, 0.25, 0.3, 0.35, 0.4]:
            trimmed_metrics = calculate_metrics(
                np.asarray(source_final.points), 
                np.asarray(target.points),
                trim_percentage=trim_percentage
            )
            print(f"  裁剪 {trim_percentage*100:.0f}%: PV={trimmed_metrics['pv_value']:.2f}μm, RMS={trimmed_metrics['rms']:.2f}μm")
            
            if trimmed_metrics['pv_value'] <= target_pv and trimmed_metrics['rms'] <= target_rms:
                print(f"    🎉 裁剪 {trim_percentage*100:.0f}% 后达到目标精度！")
                break
    
    # 7. 保存结果
    print(f"\n7. 保存结果...")
    
    np.savetxt(os.path.join(results_dir, 'final_transformation.txt'), final_transformation)
    np.savetxt(os.path.join(results_dir, 'multiscale_transformation.txt'), multiscale_transformation)
    np.savetxt(os.path.join(results_dir, 'optimization_transformation.txt'), optimization_transformation)
    
    o3d.io.write_point_cloud(os.path.join(results_dir, 'final_result.ply'), source_final)
    
    # 保存详细报告
    with open(os.path.join(results_dir, 'precision_report.txt'), 'w', encoding='utf-8') as f:
        f.write("自适应精密配准报告\n")
        f.write("=" * 40 + "\n\n")
        
        f.write("配准结果:\n")
        for stage_name, metrics, stage_time in stages:
            f.write(f"  {stage_name}: PV={metrics['pv_value']:.2f}μm, RMS={metrics['rms']:.2f}μm, 耗时={stage_time:.2f}s\n")
        
        f.write(f"\n目标精度: PV≤{target_pv}μm, RMS≤{target_rms}μm\n")
        if final_metrics['pv_value'] <= target_pv and final_metrics['rms'] <= target_rms:
            f.write("🎉 成功达到目标精度！\n")
        else:
            f.write("❌ 未达到目标精度\n")
    
    print("自适应精密配准完成!")

if __name__ == "__main__":
    main()
