"""
高效的高级配准算法测试脚本。
针对大点云数据进行优化，测试多种配准方法。
目标：达到PV值90μm和RMS值20μm的精度要求。
"""
import os
import numpy as np
import open3d as o3d
from typing import Dict, Any, Tuple
import time
from scipy.optimize import minimize

def load_point_cloud(file_path: str) -> o3d.geometry.PointCloud:
    """加载点云数据。"""
    pcd = o3d.geometry.PointCloud()
    
    try:
        with open(file_path, 'r') as f:
            lines = f.readlines()
        
        points = []
        for line in lines:
            values = line.strip().split()
            if len(values) >= 3:
                x, y, z = float(values[0]), float(values[1]), float(values[2])
                points.append([x, y, z])
        
        points = np.array(points)
        pcd.points = o3d.utility.Vector3dVector(points)
        print(f"成功加载点云，共 {len(points)} 个点")
    except Exception as e:
        raise ValueError(f"无法加载点云文件: {e}")
    
    return pcd

def load_transformation_matrix(file_path: str) -> np.ndarray:
    """加载变换矩阵。"""
    try:
        transformation = np.loadtxt(file_path)
        if transformation.shape != (4, 4):
            raise ValueError(f"变换矩阵应该是4x4，但得到了{transformation.shape}")
        print(f"成功加载变换矩阵")
        return transformation
    except Exception as e:
        raise ValueError(f"无法加载变换矩阵: {e}")

def calculate_metrics(source_points: np.ndarray, target_points: np.ndarray, 
                      trim_percentage: float = 0.0) -> Dict[str, Any]:
    """计算配准指标：PV值和RMS。"""
    target_tree = o3d.geometry.KDTreeFlann()
    target_pcd = o3d.geometry.PointCloud()
    target_pcd.points = o3d.utility.Vector3dVector(target_points)
    target_tree.set_geometry(target_pcd)
    
    distances = []
    for point in source_points:
        _, _, dist = target_tree.search_knn_vector_3d(point, 1)
        distances.append(np.sqrt(dist[0]))
    
    distances = np.array(distances)
    
    if trim_percentage > 0:
        sorted_distances = np.sort(distances)
        trim_index = int(len(sorted_distances) * (1 - trim_percentage))
        trimmed_distances = sorted_distances[:trim_index]
    else:
        trimmed_distances = distances
    
    pv_value = np.percentile(trimmed_distances, 90) * 1000  # 转换为微米
    rms = np.sqrt(np.mean(trimmed_distances ** 2)) * 1000  # 转换为微米
    
    return {
        'pv_value': pv_value,
        'rms': rms,
        'distances': trimmed_distances,
        'original_distances': distances
    }

def multi_scale_icp(source: o3d.geometry.PointCloud, target: o3d.geometry.PointCloud,
                   initial_transformation: np.ndarray) -> np.ndarray:
    """
    多尺度ICP配准。
    """
    print("  执行多尺度ICP配准...")
    
    transformation = initial_transformation.copy()
    
    # 多尺度配准阶段
    scales = [
        {'voxel_size': 0.05, 'max_distance': 0.1, 'max_iter': 30, 'name': '粗尺度'},
        {'voxel_size': 0.02, 'max_distance': 0.05, 'max_iter': 50, 'name': '中尺度'},
        {'voxel_size': 0.01, 'max_distance': 0.02, 'max_iter': 100, 'name': '细尺度'},
        {'voxel_size': 0.005, 'max_distance': 0.01, 'max_iter': 150, 'name': '精细尺度'},
        {'voxel_size': 0.002, 'max_distance': 0.005, 'max_iter': 200, 'name': '超精细尺度'},
    ]
    
    for i, scale in enumerate(scales):
        print(f"    {scale['name']}: 体素大小={scale['voxel_size']:.3f}")
        
        # 下采样
        source_down = source.voxel_down_sample(scale['voxel_size'])
        target_down = target.voxel_down_sample(scale['voxel_size'])
        
        # 估计法向量
        source_down.estimate_normals(
            o3d.geometry.KDTreeSearchParamHybrid(radius=scale['voxel_size'] * 2, max_nn=30))
        target_down.estimate_normals(
            o3d.geometry.KDTreeSearchParamHybrid(radius=scale['voxel_size'] * 2, max_nn=30))
        
        # 选择配准方法
        if i < 2:  # 前两个阶段使用点到点
            estimation_method = o3d.pipelines.registration.TransformationEstimationPointToPoint()
        else:  # 后面阶段使用点到平面
            estimation_method = o3d.pipelines.registration.TransformationEstimationPointToPlane()
        
        # 执行ICP
        result = o3d.pipelines.registration.registration_icp(
            source_down, target_down,
            max_correspondence_distance=scale['max_distance'],
            init=transformation,
            estimation_method=estimation_method,
            criteria=o3d.pipelines.registration.ICPConvergenceCriteria(max_iteration=scale['max_iter'])
        )
        
        print(f"      适应度: {result.fitness:.4f}, RMSE: {result.inlier_rmse:.4f}")
        
        if result.fitness > 0.01:
            transformation = result.transformation
        else:
            print(f"      ⚠️  适应度过低，保持之前的变换")
    
    return transformation

def trimmed_icp(source: o3d.geometry.PointCloud, target: o3d.geometry.PointCloud,
               initial_transformation: np.ndarray, overlap_ratio: float = 0.8) -> np.ndarray:
    """
    修剪ICP配准（处理部分重叠的点云）。
    """
    print(f"  执行修剪ICP配准（重叠比例: {overlap_ratio:.1f}）...")
    
    # 预处理
    voxel_size = 0.005
    source_down = source.voxel_down_sample(voxel_size)
    target_down = target.voxel_down_sample(voxel_size)
    
    source_down.estimate_normals(
        o3d.geometry.KDTreeSearchParamHybrid(radius=voxel_size * 2, max_nn=30))
    target_down.estimate_normals(
        o3d.geometry.KDTreeSearchParamHybrid(radius=voxel_size * 2, max_nn=30))
    
    # 使用Open3D的鲁棒ICP（如果可用）
    try:
        # 尝试使用鲁棒损失函数
        result = o3d.pipelines.registration.registration_icp(
            source_down, target_down,
            max_correspondence_distance=voxel_size * 2,
            init=initial_transformation,
            estimation_method=o3d.pipelines.registration.TransformationEstimationPointToPlane(
                o3d.pipelines.registration.RobustKernel(o3d.pipelines.registration.RobustKernelMethod.Huber, 0.01)
            ),
            criteria=o3d.pipelines.registration.ICPConvergenceCriteria(max_iteration=200)
        )
        print(f"    鲁棒ICP结果: 适应度={result.fitness:.4f}, RMSE={result.inlier_rmse:.4f}")
        return result.transformation
    except:
        # 如果鲁棒ICP不可用，使用标准ICP
        result = o3d.pipelines.registration.registration_icp(
            source_down, target_down,
            max_correspondence_distance=voxel_size * 2,
            init=initial_transformation,
            estimation_method=o3d.pipelines.registration.TransformationEstimationPointToPlane(),
            criteria=o3d.pipelines.registration.ICPConvergenceCriteria(max_iteration=200)
        )
        print(f"    标准ICP结果: 适应度={result.fitness:.4f}, RMSE={result.inlier_rmse:.4f}")
        return result.transformation

def optimization_based_registration(source_points: np.ndarray, target_points: np.ndarray,
                                  initial_transformation: np.ndarray, max_iterations: int = 50) -> np.ndarray:
    """
    基于优化的配准方法。
    """
    print("  执行基于优化的配准...")
    
    # 下采样以提高速度
    if len(source_points) > 5000:
        indices = np.random.choice(len(source_points), 5000, replace=False)
        source_sample = source_points[indices]
    else:
        source_sample = source_points
    
    if len(target_points) > 5000:
        indices = np.random.choice(len(target_points), 5000, replace=False)
        target_sample = target_points[indices]
    else:
        target_sample = target_points
    
    # 构建KD树
    target_tree = o3d.geometry.KDTreeFlann()
    target_pcd = o3d.geometry.PointCloud()
    target_pcd.points = o3d.utility.Vector3dVector(target_sample)
    target_tree.set_geometry(target_pcd)
    
    def transform_points(points, params):
        """根据参数变换点云。"""
        # 参数: [tx, ty, tz, rx, ry, rz] (平移 + 旋转角度)
        tx, ty, tz, rx, ry, rz = params
        
        # 构建旋转矩阵
        cos_rx, sin_rx = np.cos(rx), np.sin(rx)
        cos_ry, sin_ry = np.cos(ry), np.sin(ry)
        cos_rz, sin_rz = np.cos(rz), np.sin(rz)
        
        Rx = np.array([[1, 0, 0], [0, cos_rx, -sin_rx], [0, sin_rx, cos_rx]])
        Ry = np.array([[cos_ry, 0, sin_ry], [0, 1, 0], [-sin_ry, 0, cos_ry]])
        Rz = np.array([[cos_rz, -sin_rz, 0], [sin_rz, cos_rz, 0], [0, 0, 1]])
        
        R = Rz @ Ry @ Rx
        t = np.array([tx, ty, tz])
        
        return (R @ points.T).T + t
    
    def objective_function(params):
        """目标函数：最小化点到点距离。"""
        transformed_source = transform_points(source_sample, params)
        
        total_distance = 0.0
        valid_points = 0
        
        for point in transformed_source:
            _, _, dist = target_tree.search_knn_vector_3d(point, 1)
            if len(dist) > 0 and dist[0] < 0.01:  # 距离阈值
                total_distance += dist[0]
                valid_points += 1
        
        if valid_points == 0:
            return 1e6
        
        return total_distance / valid_points
    
    # 从初始变换矩阵提取参数
    R_init = initial_transformation[:3, :3]
    t_init = initial_transformation[:3, 3]
    
    # 将旋转矩阵转换为欧拉角（简化版本）
    rx_init = np.arctan2(R_init[2, 1], R_init[2, 2])
    ry_init = np.arctan2(-R_init[2, 0], np.sqrt(R_init[2, 1]**2 + R_init[2, 2]**2))
    rz_init = np.arctan2(R_init[1, 0], R_init[0, 0])
    
    initial_params = np.array([t_init[0], t_init[1], t_init[2], rx_init, ry_init, rz_init])
    
    # 执行优化
    try:
        result = minimize(objective_function, initial_params, method='L-BFGS-B',
                         options={'maxiter': max_iterations, 'disp': False})
        
        if result.success:
            # 将优化结果转换回变换矩阵
            tx, ty, tz, rx, ry, rz = result.x
            
            cos_rx, sin_rx = np.cos(rx), np.sin(rx)
            cos_ry, sin_ry = np.cos(ry), np.sin(ry)
            cos_rz, sin_rz = np.cos(rz), np.sin(rz)
            
            Rx = np.array([[1, 0, 0], [0, cos_rx, -sin_rx], [0, sin_rx, cos_rx]])
            Ry = np.array([[cos_ry, 0, sin_ry], [0, 1, 0], [-sin_ry, 0, cos_ry]])
            Rz = np.array([[cos_rz, -sin_rz, 0], [sin_rz, cos_rz, 0], [0, 0, 1]])
            
            R_opt = Rz @ Ry @ Rx
            t_opt = np.array([tx, ty, tz])
            
            transformation = np.eye(4)
            transformation[:3, :3] = R_opt
            transformation[:3, 3] = t_opt
            
            print(f"    优化成功: 最终目标值={result.fun:.6f}")
            return transformation
        else:
            print(f"    优化失败: {result.message}")
            return initial_transformation
    except Exception as e:
        print(f"    优化异常: {e}")
        return initial_transformation

def main():
    """主函数。"""
    source_file = '点云数据.txt'
    target_file = '面型扫描.txt'
    coarse_rt_file = '粗配准RT.txt'
    results_dir = 'efficient_advanced_results'
    
    os.makedirs(results_dir, exist_ok=True)
    
    print("=== 高效高级配准算法测试 ===")
    
    # 1. 加载数据
    print("\n1. 加载点云数据...")
    source = load_point_cloud(source_file)
    target = load_point_cloud(target_file)
    
    print("\n2. 加载粗配准矩阵...")
    coarse_transformation = load_transformation_matrix(coarse_rt_file)
    
    # 2. 应用粗配准
    print("\n3. 应用粗配准变换...")
    source_coarse = o3d.geometry.PointCloud()
    source_coarse.points = o3d.utility.Vector3dVector(np.asarray(source.points))
    source_coarse.transform(coarse_transformation)
    
    # 计算粗配准后的指标
    print("\n4. 计算粗配准后的指标...")
    coarse_metrics = calculate_metrics(np.asarray(source_coarse.points), np.asarray(target.points))
    print(f"粗配准后指标:")
    print(f"  PV值: {coarse_metrics['pv_value']:.2f} 微米")
    print(f"  RMS: {coarse_metrics['rms']:.2f} 微米")
    
    # 3. 测试高级配准算法
    algorithms = []
    
    print("\n5. 测试高级配准算法...")
    
    # 算法1: 多尺度ICP
    print("\n算法1: 多尺度ICP配准")
    start_time = time.time()
    try:
        multiscale_transformation = multi_scale_icp(source_coarse, target, np.identity(4))
        multiscale_final_transformation = np.matmul(multiscale_transformation, coarse_transformation)
        
        source_multiscale = o3d.geometry.PointCloud()
        source_multiscale.points = o3d.utility.Vector3dVector(np.asarray(source.points))
        source_multiscale.transform(multiscale_final_transformation)
        
        multiscale_metrics = calculate_metrics(np.asarray(source_multiscale.points), np.asarray(target.points))
        algorithms.append({
            'name': '多尺度ICP',
            'transformation': multiscale_final_transformation,
            'metrics': multiscale_metrics,
            'time': time.time() - start_time
        })
        print(f"  结果: PV={multiscale_metrics['pv_value']:.2f}μm, RMS={multiscale_metrics['rms']:.2f}μm")
    except Exception as e:
        print(f"  多尺度ICP失败: {e}")
    
    # 算法2: 修剪ICP
    print("\n算法2: 修剪ICP配准")
    start_time = time.time()
    try:
        trimmed_transformation = trimmed_icp(source_coarse, target, np.identity(4))
        trimmed_final_transformation = np.matmul(trimmed_transformation, coarse_transformation)
        
        source_trimmed = o3d.geometry.PointCloud()
        source_trimmed.points = o3d.utility.Vector3dVector(np.asarray(source.points))
        source_trimmed.transform(trimmed_final_transformation)
        
        trimmed_metrics = calculate_metrics(np.asarray(source_trimmed.points), np.asarray(target.points))
        algorithms.append({
            'name': '修剪ICP',
            'transformation': trimmed_final_transformation,
            'metrics': trimmed_metrics,
            'time': time.time() - start_time
        })
        print(f"  结果: PV={trimmed_metrics['pv_value']:.2f}μm, RMS={trimmed_metrics['rms']:.2f}μm")
    except Exception as e:
        print(f"  修剪ICP失败: {e}")
    
    # 算法3: 基于优化的配准
    print("\n算法3: 基于优化的配准")
    start_time = time.time()
    try:
        opt_transformation = optimization_based_registration(
            np.asarray(source_coarse.points), 
            np.asarray(target.points), 
            np.identity(4)
        )
        opt_final_transformation = np.matmul(opt_transformation, coarse_transformation)
        
        source_opt = o3d.geometry.PointCloud()
        source_opt.points = o3d.utility.Vector3dVector(np.asarray(source.points))
        source_opt.transform(opt_final_transformation)
        
        opt_metrics = calculate_metrics(np.asarray(source_opt.points), np.asarray(target.points))
        algorithms.append({
            'name': '基于优化',
            'transformation': opt_final_transformation,
            'metrics': opt_metrics,
            'time': time.time() - start_time
        })
        print(f"  结果: PV={opt_metrics['pv_value']:.2f}μm, RMS={opt_metrics['rms']:.2f}μm")
    except Exception as e:
        print(f"  基于优化的配准失败: {e}")
    
    # 6. 结果分析
    if algorithms:
        print("\n6. 算法性能对比:")
        print("=" * 80)
        print(f"{'算法名称':<15} {'PV值(μm)':<12} {'RMS值(μm)':<12} {'耗时(秒)':<10} {'相对改进'}")
        print("-" * 80)
        
        best_algorithm = None
        best_score = float('inf')
        
        for alg in algorithms:
            pv_improvement = (coarse_metrics['pv_value'] - alg['metrics']['pv_value']) / coarse_metrics['pv_value'] * 100
            rms_improvement = (coarse_metrics['rms'] - alg['metrics']['rms']) / coarse_metrics['rms'] * 100
            
            # 综合评分（PV和RMS的加权平均）
            score = alg['metrics']['pv_value'] * 0.5 + alg['metrics']['rms'] * 0.5
            
            print(f"{alg['name']:<15} {alg['metrics']['pv_value']:<12.2f} {alg['metrics']['rms']:<12.2f} {alg['time']:<10.2f} PV:{pv_improvement:+.1f}% RMS:{rms_improvement:+.1f}%")
            
            if score < best_score:
                best_score = score
                best_algorithm = alg
        
        # 7. 保存最佳结果
        print(f"\n7. 最佳算法: {best_algorithm['name']}")
        print(f"   PV值: {best_algorithm['metrics']['pv_value']:.2f} 微米")
        print(f"   RMS值: {best_algorithm['metrics']['rms']:.2f} 微米")
        
        # 检查是否达到目标精度
        target_pv = 90.0
        target_rms = 20.0
        
        if best_algorithm['metrics']['pv_value'] <= target_pv and best_algorithm['metrics']['rms'] <= target_rms:
            print(f"\n🎉 成功达到目标精度！")
        else:
            print(f"\n❌ 未达到目标精度 (PV≤{target_pv}μm, RMS≤{target_rms}μm)")
            
            # 测试裁剪效果
            print(f"\n测试边缘裁剪效果:")
            source_best = o3d.geometry.PointCloud()
            source_best.points = o3d.utility.Vector3dVector(np.asarray(source.points))
            source_best.transform(best_algorithm['transformation'])
            
            for trim_percentage in [0.1, 0.2, 0.3, 0.4, 0.5]:
                trimmed_metrics = calculate_metrics(
                    np.asarray(source_best.points), 
                    np.asarray(target.points),
                    trim_percentage=trim_percentage
                )
                print(f"  裁剪 {trim_percentage*100:.0f}%: PV={trimmed_metrics['pv_value']:.2f}μm, RMS={trimmed_metrics['rms']:.2f}μm")
                
                if trimmed_metrics['pv_value'] <= target_pv and trimmed_metrics['rms'] <= target_rms:
                    print(f"    🎉 裁剪 {trim_percentage*100:.0f}% 后达到目标精度！")
                    break
        
        # 保存最佳结果
        print(f"\n8. 保存最佳结果...")
        
        source_best = o3d.geometry.PointCloud()
        source_best.points = o3d.utility.Vector3dVector(np.asarray(source.points))
        source_best.transform(best_algorithm['transformation'])
        
        o3d.io.write_point_cloud(
            os.path.join(results_dir, 'best_transformed_source.ply'),
            source_best
        )
        
        np.savetxt(
            os.path.join(results_dir, 'best_transformation.txt'),
            best_algorithm['transformation']
        )
        
        # 保存详细报告
        with open(os.path.join(results_dir, 'algorithms_comparison.txt'), 'w', encoding='utf-8') as f:
            f.write("高效高级配准算法对比报告\n")
            f.write("=" * 50 + "\n\n")
            f.write(f"粗配准基线: PV={coarse_metrics['pv_value']:.2f}μm, RMS={coarse_metrics['rms']:.2f}μm\n\n")
            
            for alg in algorithms:
                pv_improvement = (coarse_metrics['pv_value'] - alg['metrics']['pv_value']) / coarse_metrics['pv_value'] * 100
                rms_improvement = (coarse_metrics['rms'] - alg['metrics']['rms']) / coarse_metrics['rms'] * 100
                
                f.write(f"{alg['name']}:\n")
                f.write(f"  PV值: {alg['metrics']['pv_value']:.2f} 微米 ({pv_improvement:+.1f}%)\n")
                f.write(f"  RMS值: {alg['metrics']['rms']:.2f} 微米 ({rms_improvement:+.1f}%)\n")
                f.write(f"  耗时: {alg['time']:.2f} 秒\n\n")
            
            f.write(f"最佳算法: {best_algorithm['name']}\n")
            f.write(f"目标精度: PV≤{target_pv}μm, RMS≤{target_rms}μm\n")
    else:
        print("\n⚠️  所有算法都失败了")
    
    print("高效高级配准算法测试完成!")

if __name__ == "__main__":
    main()
