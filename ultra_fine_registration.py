"""
超精细配准脚本，在改进配准的基础上进一步优化。
使用更多的配准技术来达到目标精度。
目标：达到PV值90μm和RMS值20μm的精度要求。
"""
import os
import numpy as np
import open3d as o3d
from typing import Dict, Any, Tuple
import time

def load_point_cloud(file_path: str) -> o3d.geometry.PointCloud:
    """加载点云数据。"""
    pcd = o3d.geometry.PointCloud()
    
    try:
        with open(file_path, 'r') as f:
            lines = f.readlines()
        
        points = []
        for line in lines:
            values = line.strip().split()
            if len(values) >= 3:
                x, y, z = float(values[0]), float(values[1]), float(values[2])
                points.append([x, y, z])
        
        points = np.array(points)
        pcd.points = o3d.utility.Vector3dVector(points)
        print(f"成功加载点云，共 {len(points)} 个点")
    except Exception as e:
        raise ValueError(f"无法加载点云文件: {e}")
    
    return pcd

def load_transformation_matrix(file_path: str) -> np.ndarray:
    """加载变换矩阵。"""
    try:
        transformation = np.loadtxt(file_path)
        if transformation.shape != (4, 4):
            raise ValueError(f"变换矩阵应该是4x4，但得到了{transformation.shape}")
        print(f"成功加载变换矩阵")
        return transformation
    except Exception as e:
        raise ValueError(f"无法加载变换矩阵: {e}")

def calculate_metrics(source_points: np.ndarray, target_points: np.ndarray, 
                      trim_percentage: float = 0.0) -> Dict[str, Any]:
    """计算配准指标：PV值和RMS。"""
    target_tree = o3d.geometry.KDTreeFlann()
    target_pcd = o3d.geometry.PointCloud()
    target_pcd.points = o3d.utility.Vector3dVector(target_points)
    target_tree.set_geometry(target_pcd)
    
    distances = []
    for point in source_points:
        _, _, dist = target_tree.search_knn_vector_3d(point, 1)
        distances.append(np.sqrt(dist[0]))
    
    distances = np.array(distances)
    
    if trim_percentage > 0:
        sorted_distances = np.sort(distances)
        trim_index = int(len(sorted_distances) * (1 - trim_percentage))
        trimmed_distances = sorted_distances[:trim_index]
    else:
        trimmed_distances = distances
    
    pv_value = np.percentile(trimmed_distances, 90) * 1000  # 转换为微米
    rms = np.sqrt(np.mean(trimmed_distances ** 2)) * 1000  # 转换为微米
    
    return {
        'pv_value': pv_value,
        'rms': rms,
        'distances': trimmed_distances,
        'original_distances': distances
    }

def ultra_fine_preprocess(pcd: o3d.geometry.PointCloud, voxel_size: float, 
                         remove_outliers: bool = True, outlier_std_ratio: float = 1.5) -> o3d.geometry.PointCloud:
    """超精细点云预处理。"""
    processed_pcd = o3d.geometry.PointCloud()
    processed_pcd.points = o3d.utility.Vector3dVector(np.asarray(pcd.points))
    
    if remove_outliers:
        # 更严格的离群点滤波
        processed_pcd, _ = processed_pcd.remove_statistical_outlier(
            nb_neighbors=30, std_ratio=outlier_std_ratio)
        print(f"    严格离群点滤波后: {len(processed_pcd.points)} 个点")
    
    # 下采样
    if voxel_size > 0:
        original_count = len(processed_pcd.points)
        processed_pcd = processed_pcd.voxel_down_sample(voxel_size)
        print(f"    下采样 (体素大小: {voxel_size:.4f}): {len(processed_pcd.points)} 个点")
    
    # 估计法向量（更高精度）
    processed_pcd.estimate_normals(
        o3d.geometry.KDTreeSearchParamHybrid(radius=voxel_size * 1.5, max_nn=50))
    
    return processed_pcd

def iterative_refinement(source: o3d.geometry.PointCloud, target: o3d.geometry.PointCloud,
                        initial_transformation: np.ndarray, max_iterations: int = 5) -> np.ndarray:
    """
    迭代细化配准。
    """
    transformation = initial_transformation.copy()
    
    print(f"  开始迭代细化配准（最多{max_iterations}次迭代）...")
    
    for iteration in range(max_iterations):
        print(f"\n    迭代 {iteration + 1}:")
        
        # 应用当前变换
        source_transformed = o3d.geometry.PointCloud()
        source_transformed.points = o3d.utility.Vector3dVector(np.asarray(source.points))
        source_transformed.transform(transformation)
        
        # 计算当前误差
        current_metrics = calculate_metrics(np.asarray(source_transformed.points), np.asarray(target.points))
        print(f"      当前误差: PV={current_metrics['pv_value']:.2f}μm, RMS={current_metrics['rms']:.2f}μm")
        
        # 根据当前误差调整配准参数
        error_mm = current_metrics['rms'] / 1000.0
        
        if error_mm < 0.05:  # 小于50μm
            voxel_size = 0.001
            max_distance = error_mm * 0.8
        elif error_mm < 0.1:  # 小于100μm
            voxel_size = 0.002
            max_distance = error_mm * 0.9
        else:  # 大于100μm
            voxel_size = 0.005
            max_distance = error_mm * 1.0
        
        print(f"      使用参数: 体素大小={voxel_size:.4f}, 最大距离={max_distance:.4f}")
        
        # 预处理点云
        source_processed = ultra_fine_preprocess(source_transformed, voxel_size, 
                                                remove_outliers=True, outlier_std_ratio=1.2)
        target_processed = ultra_fine_preprocess(target, voxel_size, 
                                                remove_outliers=True, outlier_std_ratio=1.2)
        
        # 执行ICP配准
        result = o3d.pipelines.registration.registration_icp(
            source_processed, target_processed, 
            max_correspondence_distance=max_distance,
            init=np.identity(4),
            estimation_method=o3d.pipelines.registration.TransformationEstimationPointToPlane(),
            criteria=o3d.pipelines.registration.ICPConvergenceCriteria(max_iteration=500)
        )
        
        print(f"      ICP结果: 适应度={result.fitness:.4f}, RMSE={result.inlier_rmse:.4f}")
        
        # 检查是否有改进
        if result.fitness > 0.01:
            new_transformation = np.matmul(result.transformation, transformation)
            
            # 验证新变换是否更好
            source_new = o3d.geometry.PointCloud()
            source_new.points = o3d.utility.Vector3dVector(np.asarray(source.points))
            source_new.transform(new_transformation)
            
            new_metrics = calculate_metrics(np.asarray(source_new.points), np.asarray(target.points))
            
            if new_metrics['rms'] < current_metrics['rms']:
                transformation = new_transformation
                print(f"      ✅ 改进: 新RMS={new_metrics['rms']:.2f}μm")
                
                # 如果改进很小，提前停止
                improvement = (current_metrics['rms'] - new_metrics['rms']) / current_metrics['rms']
                if improvement < 0.01:  # 改进小于1%
                    print(f"      改进幅度很小({improvement*100:.2f}%)，停止迭代")
                    break
            else:
                print(f"      ❌ 无改进，停止迭代")
                break
        else:
            print(f"      ❌ 适应度过低，停止迭代")
            break
    
    return transformation

def main():
    """主函数。"""
    # 使用改进配准的结果作为起点
    improved_transformation_file = 'improved_registration_results/final_transformation.txt'
    
    source_file = '点云数据.txt'
    target_file = '面型扫描.txt'
    results_dir = 'ultra_fine_registration_results'
    
    os.makedirs(results_dir, exist_ok=True)
    
    print("=== 超精细配准（基于改进配准结果）===")
    
    # 1. 加载数据
    print("\n1. 加载点云数据...")
    source = load_point_cloud(source_file)
    target = load_point_cloud(target_file)
    
    print("\n2. 加载改进配准的结果...")
    if os.path.exists(improved_transformation_file):
        improved_transformation = load_transformation_matrix(improved_transformation_file)
        print("成功加载改进配准的变换矩阵")
    else:
        print("未找到改进配准结果，请先运行 improved_registration.py")
        return
    
    # 2. 应用改进配准变换
    print("\n3. 应用改进配准变换...")
    source_improved = o3d.geometry.PointCloud()
    source_improved.points = o3d.utility.Vector3dVector(np.asarray(source.points))
    source_improved.transform(improved_transformation)
    
    # 计算改进配准后的指标
    print("\n4. 计算改进配准后的指标...")
    improved_metrics = calculate_metrics(np.asarray(source_improved.points), np.asarray(target.points))
    print(f"改进配准后指标:")
    print(f"  PV值: {improved_metrics['pv_value']:.2f} 微米")
    print(f"  RMS: {improved_metrics['rms']:.2f} 微米")
    
    # 3. 执行超精细配准
    print("\n5. 执行超精细配准...")
    start_time = time.time()
    
    ultra_fine_transformation = iterative_refinement(
        source_improved, target, np.identity(4), max_iterations=10
    )
    
    end_time = time.time()
    print(f"\n超精细配准完成，耗时: {end_time - start_time:.2f} 秒")
    
    # 4. 计算最终结果
    final_transformation = np.matmul(ultra_fine_transformation, improved_transformation)
    
    print("\n6. 应用最终变换...")
    source_final = o3d.geometry.PointCloud()
    source_final.points = o3d.utility.Vector3dVector(np.asarray(source.points))
    source_final.transform(final_transformation)
    
    print("\n7. 计算最终指标...")
    final_metrics = calculate_metrics(np.asarray(source_final.points), np.asarray(target.points))
    print(f"\n最终配准结果指标 (不裁剪):")
    print(f"  PV值: {final_metrics['pv_value']:.2f} 微米")
    print(f"  RMS: {final_metrics['rms']:.2f} 微米")
    
    # 计算总体改进程度（相对于粗配准）
    # 假设粗配准的PV值是389.21μm，RMS值是373.06μm
    coarse_pv = 389.21
    coarse_rms = 373.06
    
    total_pv_improvement = (coarse_pv - final_metrics['pv_value']) / coarse_pv * 100
    total_rms_improvement = (coarse_rms - final_metrics['rms']) / coarse_rms * 100
    
    print(f"\n总体改进程度（相对于粗配准）:")
    print(f"  PV值改进: {total_pv_improvement:.1f}%")
    print(f"  RMS改进: {total_rms_improvement:.1f}%")
    
    # 检查是否达到目标精度
    target_pv = 90.0  # 微米
    target_rms = 20.0  # 微米
    
    if final_metrics['pv_value'] <= target_pv and final_metrics['rms'] <= target_rms:
        print(f"\n🎉 成功达到目标精度！")
        print(f"   PV值: {final_metrics['pv_value']:.2f} ≤ {target_pv} 微米")
        print(f"   RMS: {final_metrics['rms']:.2f} ≤ {target_rms} 微米")
    else:
        print(f"\n❌ 未达到目标精度")
        print(f"   PV值: {final_metrics['pv_value']:.2f} > {target_pv} 微米" if final_metrics['pv_value'] > target_pv else f"   PV值: {final_metrics['pv_value']:.2f} ≤ {target_pv} 微米 ✅")
        print(f"   RMS: {final_metrics['rms']:.2f} > {target_rms} 微米" if final_metrics['rms'] > target_rms else f"   RMS: {final_metrics['rms']:.2f} ≤ {target_rms} 微米 ✅")
        
        # 尝试不同的裁剪比例
        print(f"\n尝试不同的裁剪比例:")
        best_trim = None
        for trim_percentage in [0.01, 0.02, 0.03, 0.05, 0.10, 0.15, 0.20, 0.25, 0.30, 0.35, 0.40, 0.45, 0.50]:
            trimmed_metrics = calculate_metrics(
                np.asarray(source_final.points), 
                np.asarray(target.points),
                trim_percentage=trim_percentage
            )
            print(f"  裁剪 {trim_percentage*100:.1f}%: PV={trimmed_metrics['pv_value']:.2f}μm, RMS={trimmed_metrics['rms']:.2f}μm")
            
            if trimmed_metrics['pv_value'] <= target_pv and trimmed_metrics['rms'] <= target_rms:
                print(f"    🎉 裁剪 {trim_percentage*100:.1f}% 后达到目标精度！")
                best_trim = trim_percentage
                break
    
    # 保存结果
    print(f"\n8. 保存结果到 {results_dir}/...")
    
    o3d.io.write_point_cloud(
        os.path.join(results_dir, 'final_transformed_source.ply'),
        source_final
    )
    
    np.savetxt(
        os.path.join(results_dir, 'final_transformation.txt'),
        final_transformation
    )
    
    np.savetxt(
        os.path.join(results_dir, 'ultra_fine_transformation.txt'),
        ultra_fine_transformation
    )
    
    # 保存超精细配准报告
    with open(os.path.join(results_dir, 'ultra_fine_report.txt'), 'w', encoding='utf-8') as f:
        f.write("超精细点云配准报告\n")
        f.write("=" * 50 + "\n\n")
        f.write(f"改进配准后指标:\n")
        f.write(f"  PV值: {improved_metrics['pv_value']:.2f} 微米\n")
        f.write(f"  RMS: {improved_metrics['rms']:.2f} 微米\n\n")
        f.write(f"最终配准指标:\n")
        f.write(f"  PV值: {final_metrics['pv_value']:.2f} 微米\n")
        f.write(f"  RMS: {final_metrics['rms']:.2f} 微米\n\n")
        f.write(f"总体改进程度（相对于粗配准）:\n")
        f.write(f"  PV值改进: {total_pv_improvement:.1f}%\n")
        f.write(f"  RMS改进: {total_rms_improvement:.1f}%\n\n")
        f.write(f"目标精度:\n")
        f.write(f"  PV值: ≤ {target_pv} 微米\n")
        f.write(f"  RMS: ≤ {target_rms} 微米\n\n")
        
        if final_metrics['pv_value'] <= target_pv and final_metrics['rms'] <= target_rms:
            f.write("🎉 成功达到目标精度！\n")
        else:
            f.write("❌ 未达到目标精度\n")
            if best_trim is not None:
                f.write(f"但通过裁剪 {best_trim*100:.1f}% 的边缘点可以达到目标精度\n")
    
    print("超精细点云配准完成!")

if __name__ == "__main__":
    main()
