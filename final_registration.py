"""
最终的点云配准脚本，结合粗配准矩阵和成功的ICP方法。
目标：达到PV值90μm和RMS值20μm的精度要求。
"""
import os
import numpy as np
import open3d as o3d
from typing import Dict, Any
import time

def load_point_cloud(file_path: str) -> o3d.geometry.PointCloud:
    """加载点云数据。"""
    pcd = o3d.geometry.PointCloud()
    
    try:
        with open(file_path, 'r') as f:
            lines = f.readlines()
        
        points = []
        for line in lines:
            values = line.strip().split()
            if len(values) >= 3:
                x, y, z = float(values[0]), float(values[1]), float(values[2])
                points.append([x, y, z])
        
        points = np.array(points)
        pcd.points = o3d.utility.Vector3dVector(points)
        print(f"成功加载点云，共 {len(points)} 个点")
    except Exception as e:
        raise ValueError(f"无法加载点云文件: {e}")
    
    return pcd

def load_transformation_matrix(file_path: str) -> np.ndarray:
    """加载变换矩阵。"""
    try:
        transformation = np.loadtxt(file_path)
        if transformation.shape != (4, 4):
            raise ValueError(f"变换矩阵应该是4x4，但得到了{transformation.shape}")
        print(f"成功加载粗配准矩阵")
        return transformation
    except Exception as e:
        raise ValueError(f"无法加载变换矩阵: {e}")

def preprocess_point_cloud(pcd: o3d.geometry.PointCloud, voxel_size: float = 0.01,
                         remove_outliers: bool = True, apply_smoothing: bool = True) -> o3d.geometry.PointCloud:
    """预处理点云。"""
    processed_pcd = o3d.geometry.PointCloud()
    processed_pcd.points = o3d.utility.Vector3dVector(np.asarray(pcd.points))
    
    if remove_outliers:
        print(f"  移除离群点...")
        processed_pcd, _ = processed_pcd.remove_statistical_outlier(
            nb_neighbors=20, std_ratio=2.0)
        print(f"  移除离群点后剩余 {len(processed_pcd.points)} 个点")
    
    if voxel_size > 0:
        print(f"  下采样点云 (体素大小: {voxel_size})...")
        processed_pcd = processed_pcd.voxel_down_sample(voxel_size)
        print(f"  下采样后剩余 {len(processed_pcd.points)} 个点")
    
    print("  估计法向量...")
    processed_pcd.estimate_normals(
        o3d.geometry.KDTreeSearchParamHybrid(radius=voxel_size * 2, max_nn=30))
    
    if apply_smoothing:
        print("  应用平滑滤波...")
        # 使用双边滤波替代拉普拉斯平滑
        try:
            processed_pcd = processed_pcd.filter_smooth_simple(number_of_iterations=5)
        except AttributeError:
            # 如果没有平滑方法，跳过平滑步骤
            print("    跳过平滑步骤（方法不可用）")
    
    return processed_pcd

def calculate_metrics(source_points: np.ndarray, target_points: np.ndarray, 
                      trim_percentage: float = 0.0) -> Dict[str, Any]:
    """计算配准指标：PV值和RMS。"""
    target_tree = o3d.geometry.KDTreeFlann()
    target_pcd = o3d.geometry.PointCloud()
    target_pcd.points = o3d.utility.Vector3dVector(target_points)
    target_tree.set_geometry(target_pcd)
    
    distances = []
    for point in source_points:
        _, _, dist = target_tree.search_knn_vector_3d(point, 1)
        distances.append(np.sqrt(dist[0]))
    
    distances = np.array(distances)
    
    if trim_percentage > 0:
        sorted_distances = np.sort(distances)
        trim_index = int(len(sorted_distances) * (1 - trim_percentage))
        trimmed_distances = sorted_distances[:trim_index]
    else:
        trimmed_distances = distances
    
    pv_value = np.percentile(trimmed_distances, 90) * 1000  # 转换为微米
    rms = np.sqrt(np.mean(trimmed_distances ** 2)) * 1000  # 转换为微米
    
    return {
        'pv_value': pv_value,
        'rms': rms,
        'distances': trimmed_distances,
        'original_distances': distances
    }

def main():
    """主函数。"""
    source_file = '点云数据.txt'
    target_file = '面型扫描.txt'
    coarse_rt_file = '粗配准RT.txt'
    results_dir = 'final_registration_results'
    
    os.makedirs(results_dir, exist_ok=True)
    
    print("=== 最终点云配准（粗配准+精配准）===")
    
    # 1. 加载数据
    print("\n1. 加载点云数据...")
    source = load_point_cloud(source_file)
    target = load_point_cloud(target_file)
    
    print("\n2. 加载粗配准矩阵...")
    coarse_transformation = load_transformation_matrix(coarse_rt_file)
    
    # 2. 应用粗配准
    print("\n3. 应用粗配准变换...")
    source_coarse = o3d.geometry.PointCloud()
    source_coarse.points = o3d.utility.Vector3dVector(np.asarray(source.points))
    source_coarse.transform(coarse_transformation)
    
    # 计算粗配准后的指标
    print("\n4. 计算粗配准后的指标...")
    coarse_metrics = calculate_metrics(np.asarray(source_coarse.points), np.asarray(target.points))
    print(f"粗配准后指标:")
    print(f"  PV值: {coarse_metrics['pv_value']:.2f} 微米")
    print(f"  RMS: {coarse_metrics['rms']:.2f} 微米")
    
    # 3. 使用成功的simple_icp方法进行精配准
    print("\n5. 执行精配准（使用成功的方法）...")
    start_time = time.time()
    
    # 预处理点云
    print("  预处理源点云...")
    source_processed = preprocess_point_cloud(source_coarse, voxel_size=0.01, 
                                             remove_outliers=True, apply_smoothing=True)
    
    print("  预处理目标点云...")
    target_processed = preprocess_point_cloud(target, voxel_size=0.01, 
                                             remove_outliers=True, apply_smoothing=True)
    
    # 多阶段ICP配准
    transformation = np.identity(4)
    
    # 阶段1：粗ICP
    print("  阶段1: 粗ICP配准")
    result1 = o3d.pipelines.registration.registration_icp(
        source_processed, target_processed, 
        max_correspondence_distance=0.05,
        init=transformation,
        estimation_method=o3d.pipelines.registration.TransformationEstimationPointToPoint(),
        criteria=o3d.pipelines.registration.ICPConvergenceCriteria(max_iteration=50)
    )
    transformation = result1.transformation
    print(f"    适应度: {result1.fitness:.4f}, RMSE: {result1.inlier_rmse:.4f}")
    
    # 阶段2：中等ICP
    print("  阶段2: 中等ICP配准")
    source_fine = preprocess_point_cloud(source_coarse, voxel_size=0.005, 
                                        remove_outliers=False, apply_smoothing=True)
    target_fine = preprocess_point_cloud(target, voxel_size=0.005, 
                                        remove_outliers=False, apply_smoothing=True)
    
    result2 = o3d.pipelines.registration.registration_icp(
        source_fine, target_fine, 
        max_correspondence_distance=0.02,
        init=transformation,
        estimation_method=o3d.pipelines.registration.TransformationEstimationPointToPlane(),
        criteria=o3d.pipelines.registration.ICPConvergenceCriteria(max_iteration=100)
    )
    transformation = result2.transformation
    print(f"    适应度: {result2.fitness:.4f}, RMSE: {result2.inlier_rmse:.4f}")
    
    # 阶段3：精细ICP
    print("  阶段3: 精细ICP配准")
    source_ultra_fine = preprocess_point_cloud(source_coarse, voxel_size=0.002, 
                                              remove_outliers=False, apply_smoothing=True)
    target_ultra_fine = preprocess_point_cloud(target, voxel_size=0.002, 
                                              remove_outliers=False, apply_smoothing=True)
    
    result3 = o3d.pipelines.registration.registration_icp(
        source_ultra_fine, target_ultra_fine, 
        max_correspondence_distance=0.01,
        init=transformation,
        estimation_method=o3d.pipelines.registration.TransformationEstimationPointToPlane(),
        criteria=o3d.pipelines.registration.ICPConvergenceCriteria(max_iteration=200)
    )
    transformation = result3.transformation
    print(f"    适应度: {result3.fitness:.4f}, RMSE: {result3.inlier_rmse:.4f}")
    
    end_time = time.time()
    print(f"精配准完成，耗时: {end_time - start_time:.2f} 秒")
    
    # 4. 计算最终结果
    final_transformation = np.matmul(transformation, coarse_transformation)
    
    print("\n6. 应用最终变换...")
    source_final = o3d.geometry.PointCloud()
    source_final.points = o3d.utility.Vector3dVector(np.asarray(source.points))
    source_final.transform(final_transformation)
    
    print("\n7. 计算最终指标...")
    final_metrics = calculate_metrics(np.asarray(source_final.points), np.asarray(target.points))
    print(f"\n最终配准结果指标 (不裁剪):")
    print(f"  PV值: {final_metrics['pv_value']:.2f} 微米")
    print(f"  RMS: {final_metrics['rms']:.2f} 微米")
    
    # 检查是否达到目标精度
    target_pv = 90.0  # 微米
    target_rms = 20.0  # 微米
    
    if final_metrics['pv_value'] <= target_pv and final_metrics['rms'] <= target_rms:
        print(f"\n✅ 成功达到目标精度！")
        print(f"   PV值: {final_metrics['pv_value']:.2f} ≤ {target_pv} 微米")
        print(f"   RMS: {final_metrics['rms']:.2f} ≤ {target_rms} 微米")
    else:
        print(f"\n❌ 未达到目标精度")
        print(f"   PV值: {final_metrics['pv_value']:.2f} > {target_pv} 微米" if final_metrics['pv_value'] > target_pv else f"   PV值: {final_metrics['pv_value']:.2f} ≤ {target_pv} 微米 ✅")
        print(f"   RMS: {final_metrics['rms']:.2f} > {target_rms} 微米" if final_metrics['rms'] > target_rms else f"   RMS: {final_metrics['rms']:.2f} ≤ {target_rms} 微米 ✅")
        
        # 尝试不同的裁剪比例
        print(f"\n尝试不同的裁剪比例:")
        best_trim = None
        for trim_percentage in [0.01, 0.02, 0.03, 0.05, 0.10, 0.15, 0.20, 0.25, 0.30, 0.35, 0.40]:
            trimmed_metrics = calculate_metrics(
                np.asarray(source_final.points), 
                np.asarray(target.points),
                trim_percentage=trim_percentage
            )
            print(f"  裁剪 {trim_percentage*100:.1f}%: PV={trimmed_metrics['pv_value']:.2f}μm, RMS={trimmed_metrics['rms']:.2f}μm")
            
            if trimmed_metrics['pv_value'] <= target_pv and trimmed_metrics['rms'] <= target_rms:
                print(f"    ✅ 裁剪 {trim_percentage*100:.1f}% 后达到目标精度！")
                best_trim = trim_percentage
                break
        
        if best_trim is None:
            print(f"\n⚠️  即使裁剪40%的边缘点也无法达到目标精度")
            print(f"   建议考虑：")
            print(f"   1. 检查粗配准矩阵的准确性")
            print(f"   2. 使用更高精度的配准算法")
            print(f"   3. 改进点云预处理方法")
    
    # 保存结果
    print(f"\n8. 保存结果到 {results_dir}/...")
    
    o3d.io.write_point_cloud(
        os.path.join(results_dir, 'final_transformed_source.ply'),
        source_final
    )
    
    np.savetxt(
        os.path.join(results_dir, 'final_transformation.txt'),
        final_transformation
    )
    
    np.savetxt(
        os.path.join(results_dir, 'fine_transformation.txt'),
        transformation
    )
    
    # 保存配准报告
    with open(os.path.join(results_dir, 'registration_report.txt'), 'w', encoding='utf-8') as f:
        f.write("点云配准报告\n")
        f.write("=" * 50 + "\n\n")
        f.write(f"粗配准后指标:\n")
        f.write(f"  PV值: {coarse_metrics['pv_value']:.2f} 微米\n")
        f.write(f"  RMS: {coarse_metrics['rms']:.2f} 微米\n\n")
        f.write(f"最终配准指标:\n")
        f.write(f"  PV值: {final_metrics['pv_value']:.2f} 微米\n")
        f.write(f"  RMS: {final_metrics['rms']:.2f} 微米\n\n")
        f.write(f"目标精度:\n")
        f.write(f"  PV值: ≤ {target_pv} 微米\n")
        f.write(f"  RMS: ≤ {target_rms} 微米\n\n")
        
        if final_metrics['pv_value'] <= target_pv and final_metrics['rms'] <= target_rms:
            f.write("✅ 成功达到目标精度！\n")
        else:
            f.write("❌ 未达到目标精度\n")
    
    print("最终点云配准完成!")

if __name__ == "__main__":
    main()
