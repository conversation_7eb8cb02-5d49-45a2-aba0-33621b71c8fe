"""
基于距离场的高精度配准算法。
使用距离场和局部优化来突破传统ICP的限制。
目标：达到PV值90μm和RMS值20μm的精度要求。
"""
import os
import numpy as np
import open3d as o3d
from typing import Dict, Any, Tuple
import time
from scipy.optimize import minimize
from scipy.spatial.distance import cdist
from scipy.interpolate import griddata

def load_point_cloud(file_path: str) -> o3d.geometry.PointCloud:
    """加载点云数据。"""
    pcd = o3d.geometry.PointCloud()
    
    try:
        with open(file_path, 'r') as f:
            lines = f.readlines()
        
        points = []
        for line in lines:
            values = line.strip().split()
            if len(values) >= 3:
                x, y, z = float(values[0]), float(values[1]), float(values[2])
                points.append([x, y, z])
        
        points = np.array(points)
        pcd.points = o3d.utility.Vector3dVector(points)
        print(f"成功加载点云，共 {len(points)} 个点")
    except Exception as e:
        raise ValueError(f"无法加载点云文件: {e}")
    
    return pcd

def calculate_metrics(source_points: np.ndarray, target_points: np.ndarray, 
                      trim_percentage: float = 0.0) -> Dict[str, Any]:
    """计算配准指标：PV值和RMS。"""
    target_tree = o3d.geometry.KDTreeFlann()
    target_pcd = o3d.geometry.PointCloud()
    target_pcd.points = o3d.utility.Vector3dVector(target_points)
    target_tree.set_geometry(target_pcd)
    
    distances = []
    for point in source_points:
        _, _, dist = target_tree.search_knn_vector_3d(point, 1)
        distances.append(np.sqrt(dist[0]))
    
    distances = np.array(distances)
    
    if trim_percentage > 0:
        sorted_distances = np.sort(distances)
        trim_index = int(len(sorted_distances) * (1 - trim_percentage))
        trimmed_distances = sorted_distances[:trim_index]
    else:
        trimmed_distances = distances
    
    pv_value = np.percentile(trimmed_distances, 90) * 1000  # 转换为微米
    rms = np.sqrt(np.mean(trimmed_distances ** 2)) * 1000  # 转换为微米
    
    return {
        'pv_value': pv_value,
        'rms': rms,
        'distances': trimmed_distances,
        'original_distances': distances
    }

def create_distance_field(target_points: np.ndarray, grid_resolution: float = 0.01) -> Tuple[np.ndarray, np.ndarray, np.ndarray, np.ndarray]:
    """创建目标点云的距离场。"""
    print(f"  创建距离场（分辨率: {grid_resolution:.3f}）...")
    
    # 计算边界
    min_bounds = np.min(target_points, axis=0) - grid_resolution * 5
    max_bounds = np.max(target_points, axis=0) + grid_resolution * 5
    
    # 创建网格
    x = np.arange(min_bounds[0], max_bounds[0], grid_resolution)
    y = np.arange(min_bounds[1], max_bounds[1], grid_resolution)
    z = np.arange(min_bounds[2], max_bounds[2], grid_resolution)
    
    print(f"    网格大小: {len(x)} x {len(y)} x {len(z)} = {len(x)*len(y)*len(z)} 个点")
    
    # 为了避免内存问题，我们使用分块处理
    chunk_size = 50000
    
    # 构建KD树
    target_tree = o3d.geometry.KDTreeFlann()
    target_pcd = o3d.geometry.PointCloud()
    target_pcd.points = o3d.utility.Vector3dVector(target_points)
    target_tree.set_geometry(target_pcd)
    
    # 创建网格点
    xx, yy, zz = np.meshgrid(x, y, z, indexing='ij')
    grid_points = np.column_stack([xx.ravel(), yy.ravel(), zz.ravel()])
    
    print(f"    计算 {len(grid_points)} 个网格点的距离...")
    
    distances = np.zeros(len(grid_points))
    
    # 分块处理以避免内存问题
    for i in range(0, len(grid_points), chunk_size):
        end_idx = min(i + chunk_size, len(grid_points))
        chunk_points = grid_points[i:end_idx]
        
        chunk_distances = []
        for point in chunk_points:
            _, _, dist = target_tree.search_knn_vector_3d(point, 1)
            chunk_distances.append(np.sqrt(dist[0]) if len(dist) > 0 else float('inf'))
        
        distances[i:end_idx] = chunk_distances
        
        if (i // chunk_size + 1) % 10 == 0:
            print(f"      处理进度: {i+len(chunk_points)}/{len(grid_points)} ({(i+len(chunk_points))/len(grid_points)*100:.1f}%)")
    
    # 重塑为3D网格
    distance_field = distances.reshape(xx.shape)
    
    print(f"    距离场创建完成，范围: {np.min(distances):.4f} - {np.max(distances):.4f}")
    
    return distance_field, x, y, z

def interpolate_distance_field(distance_field: np.ndarray, x: np.ndarray, y: np.ndarray, z: np.ndarray,
                              query_points: np.ndarray) -> np.ndarray:
    """在距离场中插值查询点的距离。"""
    # 创建网格坐标
    xx, yy, zz = np.meshgrid(x, y, z, indexing='ij')
    grid_points = np.column_stack([xx.ravel(), yy.ravel(), zz.ravel()])
    
    # 使用线性插值
    interpolated_distances = griddata(
        grid_points, distance_field.ravel(), query_points, 
        method='linear', fill_value=np.inf
    )
    
    return interpolated_distances

def distance_field_optimization(source_points: np.ndarray, target_points: np.ndarray,
                               initial_transformation: np.ndarray, max_iterations: int = 100) -> np.ndarray:
    """基于距离场的优化配准。"""
    print("  执行基于距离场的优化配准...")
    
    # 下采样以提高速度
    if len(source_points) > 10000:
        indices = np.random.choice(len(source_points), 10000, replace=False)
        source_sample = source_points[indices]
    else:
        source_sample = source_points
    
    if len(target_points) > 20000:
        indices = np.random.choice(len(target_points), 20000, replace=False)
        target_sample = target_points[indices]
    else:
        target_sample = target_points
    
    print(f"    使用 {len(source_sample)} 个源点和 {len(target_sample)} 个目标点")
    
    # 创建距离场
    distance_field, x, y, z = create_distance_field(target_sample, grid_resolution=0.02)
    
    def transform_points(points, params):
        """根据参数变换点云。"""
        # 参数: [tx, ty, tz, rx, ry, rz] (平移 + 旋转角度)
        tx, ty, tz, rx, ry, rz = params
        
        # 构建旋转矩阵
        cos_rx, sin_rx = np.cos(rx), np.sin(rx)
        cos_ry, sin_ry = np.cos(ry), np.sin(ry)
        cos_rz, sin_rz = np.cos(rz), np.sin(rz)
        
        Rx = np.array([[1, 0, 0], [0, cos_rx, -sin_rx], [0, sin_rx, cos_rx]])
        Ry = np.array([[cos_ry, 0, sin_ry], [0, 1, 0], [-sin_ry, 0, cos_ry]])
        Rz = np.array([[cos_rz, -sin_rz, 0], [sin_rz, cos_rz, 0], [0, 0, 1]])
        
        R = Rz @ Ry @ Rx
        t = np.array([tx, ty, tz])
        
        return (R @ points.T).T + t
    
    def objective_function(params):
        """目标函数：最小化距离场中的距离。"""
        transformed_source = transform_points(source_sample, params)
        
        # 在距离场中插值
        try:
            distances = interpolate_distance_field(distance_field, x, y, z, transformed_source)
            
            # 过滤无效值
            valid_distances = distances[np.isfinite(distances)]
            
            if len(valid_distances) == 0:
                return 1e6
            
            # 使用鲁棒损失函数
            huber_delta = 0.01
            residuals = valid_distances
            huber_loss = np.where(residuals <= huber_delta,
                                0.5 * residuals**2,
                                huber_delta * (residuals - 0.5 * huber_delta))
            
            return np.mean(huber_loss)
        except Exception as e:
            print(f"      目标函数异常: {e}")
            return 1e6
    
    # 从初始变换矩阵提取参数
    R_init = initial_transformation[:3, :3]
    t_init = initial_transformation[:3, 3]
    
    # 将旋转矩阵转换为欧拉角
    rx_init = np.arctan2(R_init[2, 1], R_init[2, 2])
    ry_init = np.arctan2(-R_init[2, 0], np.sqrt(R_init[2, 1]**2 + R_init[2, 2]**2))
    rz_init = np.arctan2(R_init[1, 0], R_init[0, 0])
    
    initial_params = np.array([t_init[0], t_init[1], t_init[2], rx_init, ry_init, rz_init])
    
    print(f"    初始参数: 平移=[{t_init[0]:.4f}, {t_init[1]:.4f}, {t_init[2]:.4f}], "
          f"旋转=[{rx_init:.4f}, {ry_init:.4f}, {rz_init:.4f}]")
    
    # 执行优化
    try:
        print("    开始优化...")
        result = minimize(objective_function, initial_params, 
                         method='L-BFGS-B',
                         options={'maxiter': max_iterations, 'disp': True})
        
        if result.success:
            # 将优化结果转换回变换矩阵
            tx, ty, tz, rx, ry, rz = result.x
            
            cos_rx, sin_rx = np.cos(rx), np.sin(rx)
            cos_ry, sin_ry = np.cos(ry), np.sin(ry)
            cos_rz, sin_rz = np.cos(rz), np.sin(rz)
            
            Rx = np.array([[1, 0, 0], [0, cos_rx, -sin_rx], [0, sin_rx, cos_rx]])
            Ry = np.array([[cos_ry, 0, sin_ry], [0, 1, 0], [-sin_ry, 0, cos_ry]])
            Rz = np.array([[cos_rz, -sin_rz, 0], [sin_rz, cos_rz, 0], [0, 0, 1]])
            
            R_opt = Rz @ Ry @ Rx
            t_opt = np.array([tx, ty, tz])
            
            transformation = np.eye(4)
            transformation[:3, :3] = R_opt
            transformation[:3, 3] = t_opt
            
            print(f"    优化成功: 最终目标值={result.fun:.6f}")
            print(f"    最终参数: 平移=[{tx:.4f}, {ty:.4f}, {tz:.4f}], "
                  f"旋转=[{rx:.4f}, {ry:.4f}, {rz:.4f}]")
            
            return transformation
        else:
            print(f"    优化失败: {result.message}")
            return initial_transformation
    except Exception as e:
        print(f"    优化异常: {e}")
        return initial_transformation

def hierarchical_registration(source: o3d.geometry.PointCloud, target: o3d.geometry.PointCloud,
                            initial_transformation: np.ndarray) -> np.ndarray:
    """分层配准：从粗到细的多层次配准。"""
    print("  执行分层配准...")
    
    transformation = initial_transformation.copy()
    
    # 分层配准参数
    levels = [
        {'voxel_size': 0.02, 'max_distance': 0.05, 'max_iter': 50, 'name': '第1层（粗）'},
        {'voxel_size': 0.01, 'max_distance': 0.02, 'max_iter': 100, 'name': '第2层（中）'},
        {'voxel_size': 0.005, 'max_distance': 0.01, 'max_iter': 150, 'name': '第3层（细）'},
        {'voxel_size': 0.002, 'max_distance': 0.005, 'max_iter': 200, 'name': '第4层（超细）'},
    ]
    
    for i, level in enumerate(levels):
        print(f"    {level['name']}: 体素大小={level['voxel_size']:.3f}")
        
        # 下采样
        source_down = source.voxel_down_sample(level['voxel_size'])
        target_down = target.voxel_down_sample(level['voxel_size'])
        
        print(f"      下采样后: 源点云={len(source_down.points)}点, 目标点云={len(target_down.points)}点")
        
        # 估计法向量
        source_down.estimate_normals(
            o3d.geometry.KDTreeSearchParamHybrid(radius=level['voxel_size'] * 2, max_nn=30))
        target_down.estimate_normals(
            o3d.geometry.KDTreeSearchParamHybrid(radius=level['voxel_size'] * 2, max_nn=30))
        
        # 执行ICP
        result = o3d.pipelines.registration.registration_icp(
            source_down, target_down,
            max_correspondence_distance=level['max_distance'],
            init=transformation,
            estimation_method=o3d.pipelines.registration.TransformationEstimationPointToPlane(),
            criteria=o3d.pipelines.registration.ICPConvergenceCriteria(max_iteration=level['max_iter'])
        )
        
        print(f"      ICP结果: 适应度={result.fitness:.4f}, RMSE={result.inlier_rmse:.4f}")
        
        if result.fitness > 0.01:
            transformation = result.transformation
            
            # 计算当前配准质量
            source_temp = o3d.geometry.PointCloud()
            source_temp.points = o3d.utility.Vector3dVector(np.asarray(source.points))
            source_temp.transform(transformation)
            
            temp_metrics = calculate_metrics(np.asarray(source_temp.points), np.asarray(target.points))
            print(f"      当前质量: PV={temp_metrics['pv_value']:.2f}μm, RMS={temp_metrics['rms']:.2f}μm")
        else:
            print(f"      ⚠️  适应度过低，保持之前的变换")
    
    return transformation

def main():
    """主函数。"""
    source_file = '点云数据.txt'
    target_file = '面型扫描.txt'
    coarse_rt_file = '粗配准RT.txt'
    results_dir = 'distance_field_results'
    
    os.makedirs(results_dir, exist_ok=True)
    
    print("=== 基于距离场的高精度配准 ===")
    
    # 1. 加载数据
    print("\n1. 加载数据...")
    source = load_point_cloud(source_file)
    target = load_point_cloud(target_file)
    coarse_transformation = np.loadtxt(coarse_rt_file)
    
    # 2. 应用粗配准
    print("\n2. 应用粗配准...")
    source_coarse = o3d.geometry.PointCloud()
    source_coarse.points = o3d.utility.Vector3dVector(np.asarray(source.points))
    source_coarse.transform(coarse_transformation)
    
    coarse_metrics = calculate_metrics(np.asarray(source_coarse.points), np.asarray(target.points))
    print(f"粗配准后: PV={coarse_metrics['pv_value']:.2f}μm, RMS={coarse_metrics['rms']:.2f}μm")
    
    # 3. 分层配准
    print("\n3. 分层配准...")
    start_time = time.time()
    hierarchical_transformation = hierarchical_registration(source_coarse, target, np.identity(4))
    hierarchical_time = time.time() - start_time
    
    hierarchical_final = np.matmul(hierarchical_transformation, coarse_transformation)
    
    source_hierarchical = o3d.geometry.PointCloud()
    source_hierarchical.points = o3d.utility.Vector3dVector(np.asarray(source.points))
    source_hierarchical.transform(hierarchical_final)
    
    hierarchical_metrics = calculate_metrics(np.asarray(source_hierarchical.points), np.asarray(target.points))
    print(f"分层配准结果: PV={hierarchical_metrics['pv_value']:.2f}μm, RMS={hierarchical_metrics['rms']:.2f}μm")
    
    # 4. 距离场优化
    print("\n4. 距离场优化...")
    start_time = time.time()
    
    distance_field_transformation = distance_field_optimization(
        np.asarray(source_hierarchical.points),
        np.asarray(target.points),
        np.identity(4),
        max_iterations=50
    )
    
    distance_field_time = time.time() - start_time
    
    final_transformation = np.matmul(distance_field_transformation, hierarchical_final)
    
    # 5. 计算最终结果
    print("\n5. 计算最终结果...")
    source_final = o3d.geometry.PointCloud()
    source_final.points = o3d.utility.Vector3dVector(np.asarray(source.points))
    source_final.transform(final_transformation)
    
    final_metrics = calculate_metrics(np.asarray(source_final.points), np.asarray(target.points))
    
    # 6. 结果总结
    print("\n6. 结果总结:")
    print("=" * 70)
    print(f"{'阶段':<20} {'PV值(μm)':<12} {'RMS值(μm)':<12} {'耗时(s)':<10} {'改进'}")
    print("-" * 70)
    
    stages = [
        ('粗配准', coarse_metrics, 0),
        ('分层配准', hierarchical_metrics, hierarchical_time),
        ('距离场优化', final_metrics, distance_field_time)
    ]
    
    for stage_name, metrics, stage_time in stages:
        if stage_name == '粗配准':
            improvement = "基线"
        else:
            pv_imp = (coarse_metrics['pv_value'] - metrics['pv_value']) / coarse_metrics['pv_value'] * 100
            rms_imp = (coarse_metrics['rms'] - metrics['rms']) / coarse_metrics['rms'] * 100
            improvement = f"PV:{pv_imp:+.1f}% RMS:{rms_imp:+.1f}%"
        
        print(f"{stage_name:<20} {metrics['pv_value']:<12.2f} {metrics['rms']:<12.2f} {stage_time:<10.2f} {improvement}")
    
    # 检查目标精度
    target_pv = 90.0
    target_rms = 20.0
    
    print(f"\n目标精度: PV≤{target_pv}μm, RMS≤{target_rms}μm")
    
    if final_metrics['pv_value'] <= target_pv and final_metrics['rms'] <= target_rms:
        print(f"🎉 成功达到目标精度！")
    else:
        print(f"❌ 未达到目标精度")
        print(f"   PV值: {final_metrics['pv_value']:.2f} {'≤' if final_metrics['pv_value'] <= target_pv else '>'} {target_pv}μm")
        print(f"   RMS值: {final_metrics['rms']:.2f} {'≤' if final_metrics['rms'] <= target_rms else '>'} {target_rms}μm")
        
        # 测试裁剪效果
        print(f"\n测试边缘裁剪效果:")
        for trim_percentage in [0.05, 0.1, 0.15, 0.2, 0.25, 0.3]:
            trimmed_metrics = calculate_metrics(
                np.asarray(source_final.points), 
                np.asarray(target.points),
                trim_percentage=trim_percentage
            )
            print(f"  裁剪 {trim_percentage*100:.0f}%: PV={trimmed_metrics['pv_value']:.2f}μm, RMS={trimmed_metrics['rms']:.2f}μm")
            
            if trimmed_metrics['pv_value'] <= target_pv and trimmed_metrics['rms'] <= target_rms:
                print(f"    🎉 裁剪 {trim_percentage*100:.0f}% 后达到目标精度！")
                break
    
    # 7. 保存结果
    print(f"\n7. 保存结果...")
    
    np.savetxt(os.path.join(results_dir, 'final_transformation.txt'), final_transformation)
    np.savetxt(os.path.join(results_dir, 'hierarchical_transformation.txt'), hierarchical_transformation)
    np.savetxt(os.path.join(results_dir, 'distance_field_transformation.txt'), distance_field_transformation)
    
    o3d.io.write_point_cloud(os.path.join(results_dir, 'final_result.ply'), source_final)
    
    print("基于距离场的高精度配准完成!")

if __name__ == "__main__":
    main()
