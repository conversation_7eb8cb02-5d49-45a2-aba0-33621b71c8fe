"""
简化的基于粗配准矩阵的精配准脚本。
目标：达到PV值90μm和RMS值20μm的精度要求。
"""
import os
import numpy as np
import open3d as o3d
from typing import Dict, Any
import time

def load_point_cloud(file_path: str) -> o3d.geometry.PointCloud:
    """加载点云数据。"""
    pcd = o3d.geometry.PointCloud()
    
    try:
        with open(file_path, 'r') as f:
            lines = f.readlines()
        
        points = []
        for line in lines:
            values = line.strip().split()
            if len(values) >= 3:
                x, y, z = float(values[0]), float(values[1]), float(values[2])
                points.append([x, y, z])
        
        points = np.array(points)
        pcd.points = o3d.utility.Vector3dVector(points)
        print(f"成功加载点云，共 {len(points)} 个点")
    except Exception as e:
        raise ValueError(f"无法加载点云文件: {e}")
    
    return pcd

def load_transformation_matrix(file_path: str) -> np.ndarray:
    """加载变换矩阵。"""
    try:
        transformation = np.loadtxt(file_path)
        if transformation.shape != (4, 4):
            raise ValueError(f"变换矩阵应该是4x4，但得到了{transformation.shape}")
        print(f"成功加载粗配准矩阵")
        return transformation
    except Exception as e:
        raise ValueError(f"无法加载变换矩阵: {e}")

def calculate_metrics(source_points: np.ndarray, target_points: np.ndarray, 
                      trim_percentage: float = 0.0) -> Dict[str, Any]:
    """计算配准指标：PV值和RMS。"""
    # 为目标点云构建KD树
    target_tree = o3d.geometry.KDTreeFlann()
    target_pcd = o3d.geometry.PointCloud()
    target_pcd.points = o3d.utility.Vector3dVector(target_points)
    target_tree.set_geometry(target_pcd)
    
    # 计算点到点的距离
    distances = []
    for point in source_points:
        _, _, dist = target_tree.search_knn_vector_3d(point, 1)
        distances.append(np.sqrt(dist[0]))
    
    distances = np.array(distances)
    
    # 如果需要裁剪，去除最远的点
    if trim_percentage > 0:
        sorted_distances = np.sort(distances)
        trim_index = int(len(sorted_distances) * (1 - trim_percentage))
        trimmed_distances = sorted_distances[:trim_index]
    else:
        trimmed_distances = distances
    
    # 计算PV值（峰谷值，90百分位数）
    pv_value = np.percentile(trimmed_distances, 90) * 1000  # 转换为微米
    
    # 计算RMS（均方根）
    rms = np.sqrt(np.mean(trimmed_distances ** 2)) * 1000  # 转换为微米
    
    return {
        'pv_value': pv_value,
        'rms': rms,
        'distances': trimmed_distances,
        'original_distances': distances
    }

def main():
    """主函数。"""
    # 设置文件路径
    source_file = '点云数据.txt'
    target_file = '面型扫描.txt'
    coarse_rt_file = '粗配准RT.txt'
    results_dir = 'fine_registration_results'
    
    # 创建结果目录
    os.makedirs(results_dir, exist_ok=True)
    
    print("=== 基于粗配准矩阵的精配准 ===")
    
    # 加载点云
    print("\n1. 加载点云数据...")
    source = load_point_cloud(source_file)
    target = load_point_cloud(target_file)
    
    # 加载粗配准矩阵
    print("\n2. 加载粗配准矩阵...")
    coarse_transformation = load_transformation_matrix(coarse_rt_file)
    
    # 应用粗配准变换
    print("\n3. 应用粗配准变换...")
    source_coarse = o3d.geometry.PointCloud()
    source_coarse.points = o3d.utility.Vector3dVector(np.asarray(source.points))
    source_coarse.transform(coarse_transformation)
    
    # 计算粗配准后的指标
    print("\n4. 计算粗配准后的指标...")
    coarse_metrics = calculate_metrics(np.asarray(source_coarse.points), np.asarray(target.points))
    print(f"粗配准后指标:")
    print(f"  PV值: {coarse_metrics['pv_value']:.2f} 微米")
    print(f"  RMS: {coarse_metrics['rms']:.2f} 微米")
    
    # 预处理点云（下采样和估计法向量）
    print("\n5. 预处理点云...")

    # 移除离群点
    source_clean, _ = source_coarse.remove_statistical_outlier(nb_neighbors=20, std_ratio=2.0)
    target_clean, _ = target.remove_statistical_outlier(nb_neighbors=20, std_ratio=2.0)
    print(f"  移除离群点后 - 源点云: {len(source_clean.points)} 个点, 目标点云: {len(target_clean.points)} 个点")

    # 执行多阶段精配准
    print("\n6. 执行多阶段精配准...")
    start_time = time.time()

    transformation = np.identity(4)

    # 阶段1：粗精配准
    print("  阶段1: 粗精配准")
    voxel_size = 0.02
    source_down1 = source_clean.voxel_down_sample(voxel_size)
    target_down1 = target_clean.voxel_down_sample(voxel_size)

    source_down1.estimate_normals(o3d.geometry.KDTreeSearchParamHybrid(radius=voxel_size * 2, max_nn=30))
    target_down1.estimate_normals(o3d.geometry.KDTreeSearchParamHybrid(radius=voxel_size * 2, max_nn=30))

    print(f"    下采样后 - 源点云: {len(source_down1.points)} 个点, 目标点云: {len(target_down1.points)} 个点")

    result1 = o3d.pipelines.registration.registration_icp(
        source_down1, target_down1,
        max_correspondence_distance=0.05,
        init=transformation,
        estimation_method=o3d.pipelines.registration.TransformationEstimationPointToPoint(),
        criteria=o3d.pipelines.registration.ICPConvergenceCriteria(max_iteration=50)
    )

    transformation = result1.transformation
    print(f"    适应度: {result1.fitness:.4f}, RMSE: {result1.inlier_rmse:.4f}")

    # 阶段2：中等精配准
    print("  阶段2: 中等精配准")
    voxel_size = 0.01
    source_down2 = source_clean.voxel_down_sample(voxel_size)
    target_down2 = target_clean.voxel_down_sample(voxel_size)

    source_down2.estimate_normals(o3d.geometry.KDTreeSearchParamHybrid(radius=voxel_size * 2, max_nn=30))
    target_down2.estimate_normals(o3d.geometry.KDTreeSearchParamHybrid(radius=voxel_size * 2, max_nn=30))

    print(f"    下采样后 - 源点云: {len(source_down2.points)} 个点, 目标点云: {len(target_down2.points)} 个点")

    result2 = o3d.pipelines.registration.registration_icp(
        source_down2, target_down2,
        max_correspondence_distance=0.02,
        init=transformation,
        estimation_method=o3d.pipelines.registration.TransformationEstimationPointToPlane(),
        criteria=o3d.pipelines.registration.ICPConvergenceCriteria(max_iteration=100)
    )

    transformation = result2.transformation
    print(f"    适应度: {result2.fitness:.4f}, RMSE: {result2.inlier_rmse:.4f}")

    # 阶段3：精细配准
    print("  阶段3: 精细配准")
    voxel_size = 0.005
    source_down3 = source_clean.voxel_down_sample(voxel_size)
    target_down3 = target_clean.voxel_down_sample(voxel_size)

    source_down3.estimate_normals(o3d.geometry.KDTreeSearchParamHybrid(radius=voxel_size * 2, max_nn=30))
    target_down3.estimate_normals(o3d.geometry.KDTreeSearchParamHybrid(radius=voxel_size * 2, max_nn=30))

    print(f"    下采样后 - 源点云: {len(source_down3.points)} 个点, 目标点云: {len(target_down3.points)} 个点")

    result3 = o3d.pipelines.registration.registration_icp(
        source_down3, target_down3,
        max_correspondence_distance=0.01,
        init=transformation,
        estimation_method=o3d.pipelines.registration.TransformationEstimationPointToPlane(),
        criteria=o3d.pipelines.registration.ICPConvergenceCriteria(max_iteration=200)
    )

    transformation = result3.transformation
    print(f"    适应度: {result3.fitness:.4f}, RMSE: {result3.inlier_rmse:.4f}")

    end_time = time.time()
    print(f"多阶段精配准完成，耗时: {end_time - start_time:.2f} 秒")

    # 计算最终变换矩阵
    final_transformation = np.matmul(transformation, coarse_transformation)
    
    # 应用最终变换
    print("\n7. 应用最终变换...")
    source_final = o3d.geometry.PointCloud()
    source_final.points = o3d.utility.Vector3dVector(np.asarray(source.points))
    source_final.transform(final_transformation)
    
    # 计算最终指标
    print("\n8. 计算最终指标...")
    final_metrics = calculate_metrics(np.asarray(source_final.points), np.asarray(target.points))
    print(f"\n最终配准结果指标 (不裁剪):")
    print(f"  PV值: {final_metrics['pv_value']:.2f} 微米")
    print(f"  RMS: {final_metrics['rms']:.2f} 微米")
    
    # 检查是否达到目标精度
    target_pv = 90.0  # 微米
    target_rms = 20.0  # 微米
    
    if final_metrics['pv_value'] <= target_pv and final_metrics['rms'] <= target_rms:
        print(f"\n✅ 成功达到目标精度！")
        print(f"   PV值: {final_metrics['pv_value']:.2f} ≤ {target_pv} 微米")
        print(f"   RMS: {final_metrics['rms']:.2f} ≤ {target_rms} 微米")
    else:
        print(f"\n❌ 未达到目标精度")
        print(f"   PV值: {final_metrics['pv_value']:.2f} > {target_pv} 微米" if final_metrics['pv_value'] > target_pv else f"   PV值: {final_metrics['pv_value']:.2f} ≤ {target_pv} 微米 ✅")
        print(f"   RMS: {final_metrics['rms']:.2f} > {target_rms} 微米" if final_metrics['rms'] > target_rms else f"   RMS: {final_metrics['rms']:.2f} ≤ {target_rms} 微米 ✅")
        
        # 尝试不同的裁剪比例
        print(f"\n尝试不同的裁剪比例:")
        for trim_percentage in [0.01, 0.02, 0.03, 0.05, 0.10]:
            trimmed_metrics = calculate_metrics(
                np.asarray(source_final.points), 
                np.asarray(target.points),
                trim_percentage=trim_percentage
            )
            print(f"  裁剪 {trim_percentage*100:.1f}%: PV={trimmed_metrics['pv_value']:.2f}μm, RMS={trimmed_metrics['rms']:.2f}μm")
            
            if trimmed_metrics['pv_value'] <= target_pv and trimmed_metrics['rms'] <= target_rms:
                print(f"    ✅ 裁剪 {trim_percentage*100:.1f}% 后达到目标精度！")
                break
    
    # 保存结果
    print(f"\n9. 保存结果到 {results_dir}/...")
    
    # 保存最终变换后的点云
    o3d.io.write_point_cloud(
        os.path.join(results_dir, 'final_transformed_source.ply'),
        source_final
    )
    
    # 保存最终变换矩阵
    np.savetxt(
        os.path.join(results_dir, 'final_transformation.txt'),
        final_transformation
    )
    
    # 保存精配准变换矩阵
    np.savetxt(
        os.path.join(results_dir, 'fine_transformation.txt'),
        transformation
    )
    
    print("精配准完成!")

if __name__ == "__main__":
    main()
