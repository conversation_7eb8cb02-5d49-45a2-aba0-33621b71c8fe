"""
测试逆变换矩阵的配准效果。
如果粗配准RT是面型数据到点云数据的变换，那么我们需要使用逆矩阵。
"""
import os
import numpy as np
import open3d as o3d
from typing import Dict, Any

def load_point_cloud(file_path: str) -> o3d.geometry.PointCloud:
    """加载点云数据。"""
    pcd = o3d.geometry.PointCloud()
    
    try:
        with open(file_path, 'r') as f:
            lines = f.readlines()
        
        points = []
        for line in lines:
            values = line.strip().split()
            if len(values) >= 3:
                x, y, z = float(values[0]), float(values[1]), float(values[2])
                points.append([x, y, z])
        
        points = np.array(points)
        pcd.points = o3d.utility.Vector3dVector(points)
        print(f"成功加载点云，共 {len(points)} 个点")
    except Exception as e:
        raise ValueError(f"无法加载点云文件: {e}")
    
    return pcd

def load_transformation_matrix(file_path: str) -> np.ndarray:
    """加载变换矩阵。"""
    try:
        transformation = np.loadtxt(file_path)
        if transformation.shape != (4, 4):
            raise ValueError(f"变换矩阵应该是4x4，但得到了{transformation.shape}")
        print(f"成功加载变换矩阵")
        return transformation
    except Exception as e:
        raise ValueError(f"无法加载变换矩阵: {e}")

def calculate_metrics(source_points: np.ndarray, target_points: np.ndarray, 
                      trim_percentage: float = 0.0) -> Dict[str, Any]:
    """计算配准指标：PV值和RMS。"""
    target_tree = o3d.geometry.KDTreeFlann()
    target_pcd = o3d.geometry.PointCloud()
    target_pcd.points = o3d.utility.Vector3dVector(target_points)
    target_tree.set_geometry(target_pcd)
    
    distances = []
    for point in source_points:
        _, _, dist = target_tree.search_knn_vector_3d(point, 1)
        distances.append(np.sqrt(dist[0]))
    
    distances = np.array(distances)
    
    if trim_percentage > 0:
        sorted_distances = np.sort(distances)
        trim_index = int(len(sorted_distances) * (1 - trim_percentage))
        trimmed_distances = sorted_distances[:trim_index]
    else:
        trimmed_distances = distances
    
    pv_value = np.percentile(trimmed_distances, 90) * 1000  # 转换为微米
    rms = np.sqrt(np.mean(trimmed_distances ** 2)) * 1000  # 转换为微米
    
    return {
        'pv_value': pv_value,
        'rms': rms,
        'distances': trimmed_distances,
        'original_distances': distances
    }

def main():
    """主函数。"""
    source_file = '点云数据.txt'
    target_file = '面型扫描.txt'
    coarse_rt_file = '粗配准RT.txt'
    
    print("=== 测试逆变换矩阵的配准效果 ===")
    
    # 加载数据
    print("\n1. 加载点云数据...")
    source = load_point_cloud(source_file)
    target = load_point_cloud(target_file)
    
    print("\n2. 加载粗配准矩阵...")
    coarse_transformation = load_transformation_matrix(coarse_rt_file)
    print("原始粗配准矩阵:")
    print(coarse_transformation)
    
    # 计算逆矩阵
    print("\n3. 计算逆变换矩阵...")
    try:
        inverse_transformation = np.linalg.inv(coarse_transformation)
        print("逆变换矩阵:")
        print(inverse_transformation)
    except np.linalg.LinAlgError:
        print("错误：无法计算逆矩阵，矩阵可能是奇异的")
        return
    
    # 测试原始变换
    print("\n4. 测试原始变换（点云数据 -> 面型扫描数据）...")
    source_original = o3d.geometry.PointCloud()
    source_original.points = o3d.utility.Vector3dVector(np.asarray(source.points))
    source_original.transform(coarse_transformation)
    
    original_metrics = calculate_metrics(np.asarray(source_original.points), np.asarray(target.points))
    print(f"原始变换后指标:")
    print(f"  PV值: {original_metrics['pv_value']:.2f} 微米")
    print(f"  RMS: {original_metrics['rms']:.2f} 微米")
    
    # 测试逆变换
    print("\n5. 测试逆变换（点云数据 -> 面型扫描数据）...")
    source_inverse = o3d.geometry.PointCloud()
    source_inverse.points = o3d.utility.Vector3dVector(np.asarray(source.points))
    source_inverse.transform(inverse_transformation)
    
    inverse_metrics = calculate_metrics(np.asarray(source_inverse.points), np.asarray(target.points))
    print(f"逆变换后指标:")
    print(f"  PV值: {inverse_metrics['pv_value']:.2f} 微米")
    print(f"  RMS: {inverse_metrics['rms']:.2f} 微米")
    
    # 比较结果
    print("\n6. 结果比较:")
    print(f"原始变换: PV={original_metrics['pv_value']:.2f}μm, RMS={original_metrics['rms']:.2f}μm")
    print(f"逆变换:   PV={inverse_metrics['pv_value']:.2f}μm, RMS={inverse_metrics['rms']:.2f}μm")
    
    if inverse_metrics['pv_value'] < original_metrics['pv_value'] and inverse_metrics['rms'] < original_metrics['rms']:
        print("\n✅ 逆变换效果更好！粗配准RT矩阵确实是面型数据到点云数据的变换。")
        print("   应该使用逆矩阵进行配准。")
        
        # 保存正确的变换矩阵
        print("\n7. 保存正确的变换矩阵...")
        np.savetxt('正确的粗配准RT.txt', inverse_transformation)
        print("已保存正确的粗配准矩阵到 '正确的粗配准RT.txt'")
        
        # 测试不同的裁剪比例
        print(f"\n8. 测试逆变换的不同裁剪比例:")
        for trim_percentage in [0.0, 0.05, 0.10, 0.15, 0.20, 0.30]:
            trimmed_metrics = calculate_metrics(
                np.asarray(source_inverse.points), 
                np.asarray(target.points),
                trim_percentage=trim_percentage
            )
            print(f"  裁剪 {trim_percentage*100:.1f}%: PV={trimmed_metrics['pv_value']:.2f}μm, RMS={trimmed_metrics['rms']:.2f}μm")
            
    elif original_metrics['pv_value'] < inverse_metrics['pv_value'] and original_metrics['rms'] < inverse_metrics['rms']:
        print("\n✅ 原始变换效果更好！粗配准RT矩阵确实是点云数据到面型数据的变换。")
        print("   应该使用原始矩阵进行配准。")
    else:
        print("\n⚠️  两种变换的效果都不理想，可能需要检查数据或变换矩阵的正确性。")

if __name__ == "__main__":
    main()
