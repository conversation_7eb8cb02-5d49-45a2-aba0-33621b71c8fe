"""
高级配准算法测试脚本。
尝试多种不同的配准方法来突破精度瓶颈。
目标：达到PV值90μm和RMS值20μm的精度要求。
"""
import os
import numpy as np
import open3d as o3d
from typing import Dict, Any, Tuple, List
import time
from scipy.optimize import minimize
from scipy.spatial.distance import cdist

def load_point_cloud(file_path: str) -> o3d.geometry.PointCloud:
    """加载点云数据。"""
    pcd = o3d.geometry.PointCloud()
    
    try:
        with open(file_path, 'r') as f:
            lines = f.readlines()
        
        points = []
        for line in lines:
            values = line.strip().split()
            if len(values) >= 3:
                x, y, z = float(values[0]), float(values[1]), float(values[2])
                points.append([x, y, z])
        
        points = np.array(points)
        pcd.points = o3d.utility.Vector3dVector(points)
        print(f"成功加载点云，共 {len(points)} 个点")
    except Exception as e:
        raise ValueError(f"无法加载点云文件: {e}")
    
    return pcd

def load_transformation_matrix(file_path: str) -> np.ndarray:
    """加载变换矩阵。"""
    try:
        transformation = np.loadtxt(file_path)
        if transformation.shape != (4, 4):
            raise ValueError(f"变换矩阵应该是4x4，但得到了{transformation.shape}")
        print(f"成功加载变换矩阵")
        return transformation
    except Exception as e:
        raise ValueError(f"无法加载变换矩阵: {e}")

def calculate_metrics(source_points: np.ndarray, target_points: np.ndarray, 
                      trim_percentage: float = 0.0) -> Dict[str, Any]:
    """计算配准指标：PV值和RMS。"""
    target_tree = o3d.geometry.KDTreeFlann()
    target_pcd = o3d.geometry.PointCloud()
    target_pcd.points = o3d.utility.Vector3dVector(target_points)
    target_tree.set_geometry(target_pcd)
    
    distances = []
    for point in source_points:
        _, _, dist = target_tree.search_knn_vector_3d(point, 1)
        distances.append(np.sqrt(dist[0]))
    
    distances = np.array(distances)
    
    if trim_percentage > 0:
        sorted_distances = np.sort(distances)
        trim_index = int(len(sorted_distances) * (1 - trim_percentage))
        trimmed_distances = sorted_distances[:trim_index]
    else:
        trimmed_distances = distances
    
    pv_value = np.percentile(trimmed_distances, 90) * 1000  # 转换为微米
    rms = np.sqrt(np.mean(trimmed_distances ** 2)) * 1000  # 转换为微米
    
    return {
        'pv_value': pv_value,
        'rms': rms,
        'distances': trimmed_distances,
        'original_distances': distances
    }

def colored_icp_registration(source: o3d.geometry.PointCloud, target: o3d.geometry.PointCloud,
                           initial_transformation: np.ndarray, voxel_size: float = 0.005) -> np.ndarray:
    """
    彩色ICP配准（使用法向量作为颜色信息）。
    """
    print("  执行彩色ICP配准...")
    
    # 预处理点云
    source_down = source.voxel_down_sample(voxel_size)
    target_down = target.voxel_down_sample(voxel_size)
    
    # 估计法向量
    source_down.estimate_normals(o3d.geometry.KDTreeSearchParamHybrid(radius=voxel_size * 2, max_nn=30))
    target_down.estimate_normals(o3d.geometry.KDTreeSearchParamHybrid(radius=voxel_size * 2, max_nn=30))
    
    # 将法向量作为颜色
    source_down.colors = o3d.utility.Vector3dVector(np.abs(np.asarray(source_down.normals)))
    target_down.colors = o3d.utility.Vector3dVector(np.abs(np.asarray(target_down.normals)))
    
    # 执行彩色ICP
    result = o3d.pipelines.registration.registration_colored_icp(
        source_down, target_down,
        max_correspondence_distance=voxel_size * 3,
        init=initial_transformation,
        estimation_method=o3d.pipelines.registration.TransformationEstimationForColoredICP(),
        criteria=o3d.pipelines.registration.ICPConvergenceCriteria(relative_fitness=1e-6,
                                                                  relative_rmse=1e-6,
                                                                  max_iteration=200)
    )
    
    print(f"    彩色ICP结果: 适应度={result.fitness:.4f}, RMSE={result.inlier_rmse:.4f}")
    return result.transformation

def feature_based_registration(source: o3d.geometry.PointCloud, target: o3d.geometry.PointCloud,
                              initial_transformation: np.ndarray, voxel_size: float = 0.01) -> np.ndarray:
    """
    基于特征的配准（FPFH特征）。
    """
    print("  执行基于FPFH特征的配准...")
    
    # 预处理点云
    source_down = source.voxel_down_sample(voxel_size)
    target_down = target.voxel_down_sample(voxel_size)
    
    # 估计法向量
    source_down.estimate_normals(o3d.geometry.KDTreeSearchParamHybrid(radius=voxel_size * 2, max_nn=30))
    target_down.estimate_normals(o3d.geometry.KDTreeSearchParamHybrid(radius=voxel_size * 2, max_nn=30))
    
    # 计算FPFH特征
    source_fpfh = o3d.pipelines.registration.compute_fpfh_feature(
        source_down, o3d.geometry.KDTreeSearchParamHybrid(radius=voxel_size * 5, max_nn=100))
    target_fpfh = o3d.pipelines.registration.compute_fpfh_feature(
        target_down, o3d.geometry.KDTreeSearchParamHybrid(radius=voxel_size * 5, max_nn=100))
    
    # RANSAC配准
    result = o3d.pipelines.registration.registration_ransac_based_on_feature_matching(
        source_down, target_down, source_fpfh, target_fpfh,
        mutual_filter=True,
        max_correspondence_distance=voxel_size * 2,
        estimation_method=o3d.pipelines.registration.TransformationEstimationPointToPoint(False),
        ransac_n=3,
        checkers=[o3d.pipelines.registration.CorrespondenceCheckerBasedOnEdgeLength(0.9),
                 o3d.pipelines.registration.CorrespondenceCheckerBasedOnDistance(voxel_size * 2)],
        criteria=o3d.pipelines.registration.RANSACConvergenceCriteria(100000, 0.999)
    )
    
    print(f"    FPFH配准结果: 适应度={result.fitness:.4f}, RMSE={result.inlier_rmse:.4f}")
    
    # 使用FPFH结果作为初始值进行ICP细化
    icp_result = o3d.pipelines.registration.registration_icp(
        source_down, target_down,
        max_correspondence_distance=voxel_size,
        init=result.transformation,
        estimation_method=o3d.pipelines.registration.TransformationEstimationPointToPlane(),
        criteria=o3d.pipelines.registration.ICPConvergenceCriteria(max_iteration=100)
    )
    
    print(f"    ICP细化结果: 适应度={icp_result.fitness:.4f}, RMSE={icp_result.inlier_rmse:.4f}")
    return icp_result.transformation

def generalized_icp_registration(source: o3d.geometry.PointCloud, target: o3d.geometry.PointCloud,
                               initial_transformation: np.ndarray, voxel_size: float = 0.005) -> np.ndarray:
    """
    广义ICP配准（使用多种距离度量）。
    """
    print("  执行广义ICP配准...")
    
    # 预处理点云
    source_down = source.voxel_down_sample(voxel_size)
    target_down = target.voxel_down_sample(voxel_size)
    
    # 估计法向量
    source_down.estimate_normals(o3d.geometry.KDTreeSearchParamHybrid(radius=voxel_size * 2, max_nn=30))
    target_down.estimate_normals(o3d.geometry.KDTreeSearchParamHybrid(radius=voxel_size * 2, max_nn=30))
    
    transformation = initial_transformation.copy()
    
    # 多阶段广义ICP
    stages = [
        {'max_distance': voxel_size * 4, 'max_iter': 50, 'method': 'point_to_point'},
        {'max_distance': voxel_size * 2, 'max_iter': 100, 'method': 'point_to_plane'},
        {'max_distance': voxel_size * 1, 'max_iter': 200, 'method': 'generalized'},
    ]
    
    for i, stage in enumerate(stages):
        print(f"    广义ICP阶段{i+1}: {stage['method']}")
        
        if stage['method'] == 'point_to_point':
            estimation_method = o3d.pipelines.registration.TransformationEstimationPointToPoint()
        elif stage['method'] == 'point_to_plane':
            estimation_method = o3d.pipelines.registration.TransformationEstimationPointToPlane()
        else:  # generalized
            # 使用点到平面的变体
            estimation_method = o3d.pipelines.registration.TransformationEstimationPointToPlane()
        
        result = o3d.pipelines.registration.registration_icp(
            source_down, target_down,
            max_correspondence_distance=stage['max_distance'],
            init=transformation,
            estimation_method=estimation_method,
            criteria=o3d.pipelines.registration.ICPConvergenceCriteria(max_iteration=stage['max_iter'])
        )
        
        print(f"      结果: 适应度={result.fitness:.4f}, RMSE={result.inlier_rmse:.4f}")
        
        if result.fitness > 0.01:
            transformation = result.transformation
    
    return transformation

def robust_point_matching(source_points: np.ndarray, target_points: np.ndarray,
                         initial_transformation: np.ndarray, max_iterations: int = 100) -> np.ndarray:
    """
    鲁棒点匹配算法（使用M-estimator）。
    """
    print("  执行鲁棒点匹配...")
    
    def transform_points(points, transformation):
        """应用变换矩阵到点云。"""
        homogeneous_points = np.hstack([points, np.ones((points.shape[0], 1))])
        transformed = (transformation @ homogeneous_points.T).T
        return transformed[:, :3]
    
    def compute_correspondences(src_pts, tgt_pts, max_distance=0.01):
        """计算对应关系。"""
        distances = cdist(src_pts, tgt_pts)
        correspondences = []
        for i in range(len(src_pts)):
            min_idx = np.argmin(distances[i])
            if distances[i, min_idx] < max_distance:
                correspondences.append((i, min_idx))
        return correspondences
    
    def huber_loss(residual, delta=0.005):
        """Huber损失函数。"""
        abs_residual = np.abs(residual)
        return np.where(abs_residual <= delta, 
                       0.5 * residual**2, 
                       delta * (abs_residual - 0.5 * delta))
    
    # 下采样以提高速度
    if len(source_points) > 10000:
        indices = np.random.choice(len(source_points), 10000, replace=False)
        source_sample = source_points[indices]
    else:
        source_sample = source_points
    
    if len(target_points) > 10000:
        indices = np.random.choice(len(target_points), 10000, replace=False)
        target_sample = target_points[indices]
    else:
        target_sample = target_points
    
    transformation = initial_transformation.copy()
    
    for iteration in range(max_iterations):
        # 变换源点云
        transformed_source = transform_points(source_sample, transformation)
        
        # 计算对应关系
        correspondences = compute_correspondences(transformed_source, target_sample, max_distance=0.02)
        
        if len(correspondences) < 100:
            print(f"      迭代{iteration+1}: 对应点太少({len(correspondences)})，停止")
            break
        
        # 提取对应点对
        src_corr = np.array([transformed_source[i] for i, j in correspondences])
        tgt_corr = np.array([target_sample[j] for i, j in correspondences])
        
        # 计算残差
        residuals = np.linalg.norm(src_corr - tgt_corr, axis=1)
        
        # 使用Huber损失加权
        weights = 1.0 / (1.0 + huber_loss(residuals))
        weights = weights / np.sum(weights)
        
        # 加权最小二乘求解变换
        try:
            # 计算加权质心
            src_centroid = np.average(src_corr, weights=weights, axis=0)
            tgt_centroid = np.average(tgt_corr, weights=weights, axis=0)
            
            # 去质心
            src_centered = src_corr - src_centroid
            tgt_centered = tgt_corr - tgt_centroid
            
            # 加权协方差矩阵
            H = np.zeros((3, 3))
            for i in range(len(src_centered)):
                H += weights[i] * np.outer(src_centered[i], tgt_centered[i])
            
            # SVD分解求旋转矩阵
            U, S, Vt = np.linalg.svd(H)
            R = Vt.T @ U.T
            
            # 确保旋转矩阵的行列式为正
            if np.linalg.det(R) < 0:
                Vt[-1, :] *= -1
                R = Vt.T @ U.T
            
            # 计算平移向量
            t = tgt_centroid - R @ src_centroid
            
            # 构建变换矩阵
            delta_transformation = np.eye(4)
            delta_transformation[:3, :3] = R
            delta_transformation[:3, 3] = t
            
            # 更新变换
            transformation = delta_transformation @ transformation
            
            # 计算收敛性
            mean_residual = np.mean(residuals)
            if iteration % 10 == 0:
                print(f"      迭代{iteration+1}: 对应点={len(correspondences)}, 平均残差={mean_residual:.6f}")
            
            if mean_residual < 1e-6:
                print(f"      收敛于迭代{iteration+1}")
                break
                
        except np.linalg.LinAlgError:
            print(f"      迭代{iteration+1}: 数值不稳定，停止")
            break
    
    return transformation

def main():
    """主函数。"""
    source_file = '点云数据.txt'
    target_file = '面型扫描.txt'
    coarse_rt_file = '粗配准RT.txt'
    results_dir = 'advanced_algorithms_results'
    
    os.makedirs(results_dir, exist_ok=True)
    
    print("=== 高级配准算法测试 ===")
    
    # 1. 加载数据
    print("\n1. 加载点云数据...")
    source = load_point_cloud(source_file)
    target = load_point_cloud(target_file)
    
    print("\n2. 加载粗配准矩阵...")
    coarse_transformation = load_transformation_matrix(coarse_rt_file)
    
    # 2. 应用粗配准
    print("\n3. 应用粗配准变换...")
    source_coarse = o3d.geometry.PointCloud()
    source_coarse.points = o3d.utility.Vector3dVector(np.asarray(source.points))
    source_coarse.transform(coarse_transformation)
    
    # 计算粗配准后的指标
    print("\n4. 计算粗配准后的指标...")
    coarse_metrics = calculate_metrics(np.asarray(source_coarse.points), np.asarray(target.points))
    print(f"粗配准后指标:")
    print(f"  PV值: {coarse_metrics['pv_value']:.2f} 微米")
    print(f"  RMS: {coarse_metrics['rms']:.2f} 微米")
    
    # 3. 测试不同的高级配准算法
    algorithms = []
    
    print("\n5. 测试高级配准算法...")
    
    # 算法1: 彩色ICP
    print("\n算法1: 彩色ICP配准")
    start_time = time.time()
    try:
        colored_transformation = colored_icp_registration(source_coarse, target, np.identity(4))
        colored_final_transformation = np.matmul(colored_transformation, coarse_transformation)
        
        source_colored = o3d.geometry.PointCloud()
        source_colored.points = o3d.utility.Vector3dVector(np.asarray(source.points))
        source_colored.transform(colored_final_transformation)
        
        colored_metrics = calculate_metrics(np.asarray(source_colored.points), np.asarray(target.points))
        algorithms.append({
            'name': '彩色ICP',
            'transformation': colored_final_transformation,
            'metrics': colored_metrics,
            'time': time.time() - start_time
        })
        print(f"  结果: PV={colored_metrics['pv_value']:.2f}μm, RMS={colored_metrics['rms']:.2f}μm")
    except Exception as e:
        print(f"  彩色ICP失败: {e}")
    
    # 算法2: 基于特征的配准
    print("\n算法2: 基于FPFH特征的配准")
    start_time = time.time()
    try:
        feature_transformation = feature_based_registration(source_coarse, target, np.identity(4))
        feature_final_transformation = np.matmul(feature_transformation, coarse_transformation)
        
        source_feature = o3d.geometry.PointCloud()
        source_feature.points = o3d.utility.Vector3dVector(np.asarray(source.points))
        source_feature.transform(feature_final_transformation)
        
        feature_metrics = calculate_metrics(np.asarray(source_feature.points), np.asarray(target.points))
        algorithms.append({
            'name': '基于FPFH特征',
            'transformation': feature_final_transformation,
            'metrics': feature_metrics,
            'time': time.time() - start_time
        })
        print(f"  结果: PV={feature_metrics['pv_value']:.2f}μm, RMS={feature_metrics['rms']:.2f}μm")
    except Exception as e:
        print(f"  基于特征的配准失败: {e}")
    
    # 算法3: 广义ICP
    print("\n算法3: 广义ICP配准")
    start_time = time.time()
    try:
        gicp_transformation = generalized_icp_registration(source_coarse, target, np.identity(4))
        gicp_final_transformation = np.matmul(gicp_transformation, coarse_transformation)
        
        source_gicp = o3d.geometry.PointCloud()
        source_gicp.points = o3d.utility.Vector3dVector(np.asarray(source.points))
        source_gicp.transform(gicp_final_transformation)
        
        gicp_metrics = calculate_metrics(np.asarray(source_gicp.points), np.asarray(target.points))
        algorithms.append({
            'name': '广义ICP',
            'transformation': gicp_final_transformation,
            'metrics': gicp_metrics,
            'time': time.time() - start_time
        })
        print(f"  结果: PV={gicp_metrics['pv_value']:.2f}μm, RMS={gicp_metrics['rms']:.2f}μm")
    except Exception as e:
        print(f"  广义ICP失败: {e}")
    
    # 算法4: 鲁棒点匹配
    print("\n算法4: 鲁棒点匹配")
    start_time = time.time()
    try:
        robust_transformation = robust_point_matching(
            np.asarray(source_coarse.points), 
            np.asarray(target.points), 
            np.identity(4)
        )
        robust_final_transformation = np.matmul(robust_transformation, coarse_transformation)
        
        source_robust = o3d.geometry.PointCloud()
        source_robust.points = o3d.utility.Vector3dVector(np.asarray(source.points))
        source_robust.transform(robust_final_transformation)
        
        robust_metrics = calculate_metrics(np.asarray(source_robust.points), np.asarray(target.points))
        algorithms.append({
            'name': '鲁棒点匹配',
            'transformation': robust_final_transformation,
            'metrics': robust_metrics,
            'time': time.time() - start_time
        })
        print(f"  结果: PV={robust_metrics['pv_value']:.2f}μm, RMS={robust_metrics['rms']:.2f}μm")
    except Exception as e:
        print(f"  鲁棒点匹配失败: {e}")
    
    # 6. 结果分析
    print("\n6. 算法性能对比:")
    print("=" * 80)
    print(f"{'算法名称':<15} {'PV值(μm)':<12} {'RMS值(μm)':<12} {'耗时(秒)':<10} {'相对改进'}")
    print("-" * 80)
    
    best_algorithm = None
    best_score = float('inf')
    
    for alg in algorithms:
        pv_improvement = (coarse_metrics['pv_value'] - alg['metrics']['pv_value']) / coarse_metrics['pv_value'] * 100
        rms_improvement = (coarse_metrics['rms'] - alg['metrics']['rms']) / coarse_metrics['rms'] * 100
        
        # 综合评分（PV和RMS的加权平均）
        score = alg['metrics']['pv_value'] * 0.5 + alg['metrics']['rms'] * 0.5
        
        print(f"{alg['name']:<15} {alg['metrics']['pv_value']:<12.2f} {alg['metrics']['rms']:<12.2f} {alg['time']:<10.2f} PV:{pv_improvement:+.1f}% RMS:{rms_improvement:+.1f}%")
        
        if score < best_score:
            best_score = score
            best_algorithm = alg
    
    # 7. 保存最佳结果
    if best_algorithm:
        print(f"\n7. 最佳算法: {best_algorithm['name']}")
        print(f"   PV值: {best_algorithm['metrics']['pv_value']:.2f} 微米")
        print(f"   RMS值: {best_algorithm['metrics']['rms']:.2f} 微米")
        
        # 检查是否达到目标精度
        target_pv = 90.0
        target_rms = 20.0
        
        if best_algorithm['metrics']['pv_value'] <= target_pv and best_algorithm['metrics']['rms'] <= target_rms:
            print(f"\n🎉 成功达到目标精度！")
        else:
            print(f"\n❌ 未达到目标精度 (PV≤{target_pv}μm, RMS≤{target_rms}μm)")
            
            # 测试裁剪效果
            print(f"\n测试边缘裁剪效果:")
            source_best = o3d.geometry.PointCloud()
            source_best.points = o3d.utility.Vector3dVector(np.asarray(source.points))
            source_best.transform(best_algorithm['transformation'])
            
            for trim_percentage in [0.1, 0.2, 0.3, 0.4, 0.5]:
                trimmed_metrics = calculate_metrics(
                    np.asarray(source_best.points), 
                    np.asarray(target.points),
                    trim_percentage=trim_percentage
                )
                print(f"  裁剪 {trim_percentage*100:.0f}%: PV={trimmed_metrics['pv_value']:.2f}μm, RMS={trimmed_metrics['rms']:.2f}μm")
                
                if trimmed_metrics['pv_value'] <= target_pv and trimmed_metrics['rms'] <= target_rms:
                    print(f"    🎉 裁剪 {trim_percentage*100:.0f}% 后达到目标精度！")
                    break
        
        # 保存最佳结果
        print(f"\n8. 保存最佳结果到 {results_dir}/...")
        
        source_best = o3d.geometry.PointCloud()
        source_best.points = o3d.utility.Vector3dVector(np.asarray(source.points))
        source_best.transform(best_algorithm['transformation'])
        
        o3d.io.write_point_cloud(
            os.path.join(results_dir, 'best_transformed_source.ply'),
            source_best
        )
        
        np.savetxt(
            os.path.join(results_dir, 'best_transformation.txt'),
            best_algorithm['transformation']
        )
        
        # 保存详细报告
        with open(os.path.join(results_dir, 'algorithms_comparison.txt'), 'w', encoding='utf-8') as f:
            f.write("高级配准算法对比报告\n")
            f.write("=" * 50 + "\n\n")
            f.write(f"粗配准基线: PV={coarse_metrics['pv_value']:.2f}μm, RMS={coarse_metrics['rms']:.2f}μm\n\n")
            
            for alg in algorithms:
                pv_improvement = (coarse_metrics['pv_value'] - alg['metrics']['pv_value']) / coarse_metrics['pv_value'] * 100
                rms_improvement = (coarse_metrics['rms'] - alg['metrics']['rms']) / coarse_metrics['rms'] * 100
                
                f.write(f"{alg['name']}:\n")
                f.write(f"  PV值: {alg['metrics']['pv_value']:.2f} 微米 ({pv_improvement:+.1f}%)\n")
                f.write(f"  RMS值: {alg['metrics']['rms']:.2f} 微米 ({rms_improvement:+.1f}%)\n")
                f.write(f"  耗时: {alg['time']:.2f} 秒\n\n")
            
            f.write(f"最佳算法: {best_algorithm['name']}\n")
            f.write(f"目标精度: PV≤{target_pv}μm, RMS≤{target_rms}μm\n")
    
    print("高级配准算法测试完成!")

if __name__ == "__main__":
    main()
