"""
改进的点云配准脚本，针对大初始误差进行优化。
使用更鲁棒的渐进式配准方法。
目标：达到PV值90μm和RMS值20μm的精度要求。
"""
import os
import numpy as np
import open3d as o3d
from typing import Dict, Any, Tuple
import time

def load_point_cloud(file_path: str) -> o3d.geometry.PointCloud:
    """加载点云数据。"""
    pcd = o3d.geometry.PointCloud()
    
    try:
        with open(file_path, 'r') as f:
            lines = f.readlines()
        
        points = []
        for line in lines:
            values = line.strip().split()
            if len(values) >= 3:
                x, y, z = float(values[0]), float(values[1]), float(values[2])
                points.append([x, y, z])
        
        points = np.array(points)
        pcd.points = o3d.utility.Vector3dVector(points)
        print(f"成功加载点云，共 {len(points)} 个点")
    except Exception as e:
        raise ValueError(f"无法加载点云文件: {e}")
    
    return pcd

def load_transformation_matrix(file_path: str) -> np.ndarray:
    """加载变换矩阵。"""
    try:
        transformation = np.loadtxt(file_path)
        if transformation.shape != (4, 4):
            raise ValueError(f"变换矩阵应该是4x4，但得到了{transformation.shape}")
        print(f"成功加载粗配准矩阵")
        return transformation
    except Exception as e:
        raise ValueError(f"无法加载变换矩阵: {e}")

def calculate_metrics(source_points: np.ndarray, target_points: np.ndarray, 
                      trim_percentage: float = 0.0) -> Dict[str, Any]:
    """计算配准指标：PV值和RMS。"""
    target_tree = o3d.geometry.KDTreeFlann()
    target_pcd = o3d.geometry.PointCloud()
    target_pcd.points = o3d.utility.Vector3dVector(target_points)
    target_tree.set_geometry(target_pcd)
    
    distances = []
    for point in source_points:
        _, _, dist = target_tree.search_knn_vector_3d(point, 1)
        distances.append(np.sqrt(dist[0]))
    
    distances = np.array(distances)
    
    if trim_percentage > 0:
        sorted_distances = np.sort(distances)
        trim_index = int(len(sorted_distances) * (1 - trim_percentage))
        trimmed_distances = sorted_distances[:trim_index]
    else:
        trimmed_distances = distances
    
    pv_value = np.percentile(trimmed_distances, 90) * 1000  # 转换为微米
    rms = np.sqrt(np.mean(trimmed_distances ** 2)) * 1000  # 转换为微米
    
    return {
        'pv_value': pv_value,
        'rms': rms,
        'distances': trimmed_distances,
        'original_distances': distances
    }

def robust_preprocess(pcd: o3d.geometry.PointCloud, voxel_size: float, 
                     remove_outliers: bool = True) -> o3d.geometry.PointCloud:
    """鲁棒的点云预处理。"""
    processed_pcd = o3d.geometry.PointCloud()
    processed_pcd.points = o3d.utility.Vector3dVector(np.asarray(pcd.points))
    
    if remove_outliers:
        # 统计离群点滤波
        processed_pcd, _ = processed_pcd.remove_statistical_outlier(
            nb_neighbors=20, std_ratio=2.0)
        print(f"    移除离群点后: {len(processed_pcd.points)} 个点")
    
    # 下采样
    if voxel_size > 0:
        original_count = len(processed_pcd.points)
        processed_pcd = processed_pcd.voxel_down_sample(voxel_size)
        print(f"    下采样 (体素大小: {voxel_size:.3f}): {len(processed_pcd.points)} 个点 (减少了 {original_count - len(processed_pcd.points)} 个点)")
    
    # 估计法向量
    processed_pcd.estimate_normals(
        o3d.geometry.KDTreeSearchParamHybrid(radius=voxel_size * 2, max_nn=30))
    
    return processed_pcd

def adaptive_icp_registration(source: o3d.geometry.PointCloud, target: o3d.geometry.PointCloud,
                            initial_transformation: np.ndarray, 
                            initial_error: float) -> Tuple[np.ndarray, float, float]:
    """
    自适应ICP配准，根据初始误差调整参数。
    
    参数:
        source: 源点云
        target: 目标点云
        initial_transformation: 初始变换矩阵
        initial_error: 初始误差（微米）
        
    返回:
        (最终变换矩阵, 最终适应度, 最终RMSE)
    """
    transformation = initial_transformation.copy()
    
    # 根据初始误差自适应调整配准参数
    error_mm = initial_error / 1000.0  # 转换为毫米
    
    # 定义渐进式配准阶段
    stages = []
    
    if error_mm > 0.2:  # 大于200μm
        stages.extend([
            {'voxel_size': 0.1, 'max_distance': error_mm * 2, 'max_iter': 30, 'method': 'point_to_point', 'name': '超粗配准'},
            {'voxel_size': 0.05, 'max_distance': error_mm * 1.5, 'max_iter': 50, 'method': 'point_to_point', 'name': '粗配准'},
        ])
    
    if error_mm > 0.1:  # 大于100μm
        stages.extend([
            {'voxel_size': 0.02, 'max_distance': error_mm * 1.0, 'max_iter': 100, 'method': 'point_to_point', 'name': '中等配准'},
            {'voxel_size': 0.01, 'max_distance': error_mm * 0.7, 'max_iter': 150, 'method': 'point_to_plane', 'name': '精细配准'},
        ])
    
    # 最终精配准阶段
    stages.extend([
        {'voxel_size': 0.005, 'max_distance': error_mm * 0.5, 'max_iter': 200, 'method': 'point_to_plane', 'name': '超精细配准'},
        {'voxel_size': 0.002, 'max_distance': error_mm * 0.3, 'max_iter': 300, 'method': 'point_to_plane', 'name': '极精细配准'},
    ])
    
    print(f"  根据初始误差 {initial_error:.1f}μm 设计了 {len(stages)} 个配准阶段")
    
    final_fitness = 0.0
    final_rmse = float('inf')
    
    for i, stage in enumerate(stages):
        print(f"\n  阶段{i+1}: {stage['name']}")
        print(f"    体素大小: {stage['voxel_size']:.3f}, 最大距离: {stage['max_distance']:.3f}")
        
        # 预处理点云
        source_processed = robust_preprocess(source, stage['voxel_size'], remove_outliers=(i==0))
        target_processed = robust_preprocess(target, stage['voxel_size'], remove_outliers=(i==0))
        
        # 选择配准方法
        if stage['method'] == 'point_to_point':
            estimation_method = o3d.pipelines.registration.TransformationEstimationPointToPoint()
        else:
            estimation_method = o3d.pipelines.registration.TransformationEstimationPointToPlane()
        
        # 执行ICP配准
        result = o3d.pipelines.registration.registration_icp(
            source_processed, target_processed, 
            max_correspondence_distance=stage['max_distance'],
            init=transformation,
            estimation_method=estimation_method,
            criteria=o3d.pipelines.registration.ICPConvergenceCriteria(max_iteration=stage['max_iter'])
        )
        
        print(f"    适应度: {result.fitness:.4f}, RMSE: {result.inlier_rmse:.4f}")
        
        # 检查配准是否有效
        if result.fitness > 0.01:  # 适应度阈值
            transformation = result.transformation
            final_fitness = result.fitness
            final_rmse = result.inlier_rmse
            print(f"    ✅ 配准成功")
        else:
            print(f"    ⚠️  适应度过低，保持之前的变换")
            if i == 0:  # 如果第一阶段就失败，尝试更大的距离阈值
                print(f"    尝试更大的距离阈值...")
                result_large = o3d.pipelines.registration.registration_icp(
                    source_processed, target_processed, 
                    max_correspondence_distance=stage['max_distance'] * 3,
                    init=transformation,
                    estimation_method=estimation_method,
                    criteria=o3d.pipelines.registration.ICPConvergenceCriteria(max_iteration=stage['max_iter'])
                )
                if result_large.fitness > 0.01:
                    transformation = result_large.transformation
                    final_fitness = result_large.fitness
                    final_rmse = result_large.inlier_rmse
                    print(f"    ✅ 大距离阈值配准成功: 适应度 {result_large.fitness:.4f}")
                else:
                    print(f"    ❌ 配准失败，可能需要检查数据")
                    break
    
    return transformation, final_fitness, final_rmse

def main():
    """主函数。"""
    source_file = '点云数据.txt'
    target_file = '面型扫描.txt'
    coarse_rt_file = '粗配准RT.txt'
    results_dir = 'improved_registration_results'
    
    os.makedirs(results_dir, exist_ok=True)
    
    print("=== 改进的点云配准（针对大初始误差优化）===")
    
    # 1. 加载数据
    print("\n1. 加载点云数据...")
    source = load_point_cloud(source_file)
    target = load_point_cloud(target_file)
    
    print("\n2. 加载粗配准矩阵...")
    coarse_transformation = load_transformation_matrix(coarse_rt_file)
    
    # 2. 应用粗配准
    print("\n3. 应用粗配准变换...")
    source_coarse = o3d.geometry.PointCloud()
    source_coarse.points = o3d.utility.Vector3dVector(np.asarray(source.points))
    source_coarse.transform(coarse_transformation)
    
    # 计算粗配准后的指标
    print("\n4. 计算粗配准后的指标...")
    coarse_metrics = calculate_metrics(np.asarray(source_coarse.points), np.asarray(target.points))
    print(f"粗配准后指标:")
    print(f"  PV值: {coarse_metrics['pv_value']:.2f} 微米")
    print(f"  RMS: {coarse_metrics['rms']:.2f} 微米")
    
    # 3. 执行自适应精配准
    print("\n5. 执行自适应精配准...")
    start_time = time.time()
    
    fine_transformation, final_fitness, final_rmse = adaptive_icp_registration(
        source_coarse, target, np.identity(4), coarse_metrics['rms']
    )
    
    end_time = time.time()
    print(f"\n自适应精配准完成，耗时: {end_time - start_time:.2f} 秒")
    print(f"最终适应度: {final_fitness:.4f}, 最终RMSE: {final_rmse:.4f}")
    
    # 4. 计算最终结果
    final_transformation = np.matmul(fine_transformation, coarse_transformation)
    
    print("\n6. 应用最终变换...")
    source_final = o3d.geometry.PointCloud()
    source_final.points = o3d.utility.Vector3dVector(np.asarray(source.points))
    source_final.transform(final_transformation)
    
    print("\n7. 计算最终指标...")
    final_metrics = calculate_metrics(np.asarray(source_final.points), np.asarray(target.points))
    print(f"\n最终配准结果指标 (不裁剪):")
    print(f"  PV值: {final_metrics['pv_value']:.2f} 微米")
    print(f"  RMS: {final_metrics['rms']:.2f} 微米")
    
    # 计算改进程度
    pv_improvement = (coarse_metrics['pv_value'] - final_metrics['pv_value']) / coarse_metrics['pv_value'] * 100
    rms_improvement = (coarse_metrics['rms'] - final_metrics['rms']) / coarse_metrics['rms'] * 100
    print(f"\n改进程度:")
    print(f"  PV值改进: {pv_improvement:.1f}%")
    print(f"  RMS改进: {rms_improvement:.1f}%")
    
    # 检查是否达到目标精度
    target_pv = 90.0  # 微米
    target_rms = 20.0  # 微米
    
    if final_metrics['pv_value'] <= target_pv and final_metrics['rms'] <= target_rms:
        print(f"\n✅ 成功达到目标精度！")
        print(f"   PV值: {final_metrics['pv_value']:.2f} ≤ {target_pv} 微米")
        print(f"   RMS: {final_metrics['rms']:.2f} ≤ {target_rms} 微米")
    else:
        print(f"\n❌ 未达到目标精度")
        print(f"   PV值: {final_metrics['pv_value']:.2f} > {target_pv} 微米" if final_metrics['pv_value'] > target_pv else f"   PV值: {final_metrics['pv_value']:.2f} ≤ {target_pv} 微米 ✅")
        print(f"   RMS: {final_metrics['rms']:.2f} > {target_rms} 微米" if final_metrics['rms'] > target_rms else f"   RMS: {final_metrics['rms']:.2f} ≤ {target_rms} 微米 ✅")
        
        # 尝试不同的裁剪比例
        print(f"\n尝试不同的裁剪比例:")
        best_trim = None
        for trim_percentage in [0.01, 0.02, 0.03, 0.05, 0.10, 0.15, 0.20, 0.25, 0.30]:
            trimmed_metrics = calculate_metrics(
                np.asarray(source_final.points), 
                np.asarray(target.points),
                trim_percentage=trim_percentage
            )
            print(f"  裁剪 {trim_percentage*100:.1f}%: PV={trimmed_metrics['pv_value']:.2f}μm, RMS={trimmed_metrics['rms']:.2f}μm")
            
            if trimmed_metrics['pv_value'] <= target_pv and trimmed_metrics['rms'] <= target_rms:
                print(f"    ✅ 裁剪 {trim_percentage*100:.1f}% 后达到目标精度！")
                best_trim = trim_percentage
                break
    
    # 保存结果
    print(f"\n8. 保存结果到 {results_dir}/...")
    
    o3d.io.write_point_cloud(
        os.path.join(results_dir, 'final_transformed_source.ply'),
        source_final
    )
    
    np.savetxt(
        os.path.join(results_dir, 'final_transformation.txt'),
        final_transformation
    )
    
    np.savetxt(
        os.path.join(results_dir, 'fine_transformation.txt'),
        fine_transformation
    )
    
    # 保存详细报告
    with open(os.path.join(results_dir, 'detailed_report.txt'), 'w', encoding='utf-8') as f:
        f.write("改进的点云配准详细报告\n")
        f.write("=" * 50 + "\n\n")
        f.write(f"粗配准后指标:\n")
        f.write(f"  PV值: {coarse_metrics['pv_value']:.2f} 微米\n")
        f.write(f"  RMS: {coarse_metrics['rms']:.2f} 微米\n\n")
        f.write(f"最终配准指标:\n")
        f.write(f"  PV值: {final_metrics['pv_value']:.2f} 微米\n")
        f.write(f"  RMS: {final_metrics['rms']:.2f} 微米\n\n")
        f.write(f"改进程度:\n")
        f.write(f"  PV值改进: {pv_improvement:.1f}%\n")
        f.write(f"  RMS改进: {rms_improvement:.1f}%\n\n")
        f.write(f"配准质量:\n")
        f.write(f"  最终适应度: {final_fitness:.4f}\n")
        f.write(f"  最终RMSE: {final_rmse:.4f}\n\n")
        f.write(f"目标精度:\n")
        f.write(f"  PV值: ≤ {target_pv} 微米\n")
        f.write(f"  RMS: ≤ {target_rms} 微米\n\n")
        
        if final_metrics['pv_value'] <= target_pv and final_metrics['rms'] <= target_rms:
            f.write("✅ 成功达到目标精度！\n")
        else:
            f.write("❌ 未达到目标精度\n")
    
    print("改进的点云配准完成!")

if __name__ == "__main__":
    main()
