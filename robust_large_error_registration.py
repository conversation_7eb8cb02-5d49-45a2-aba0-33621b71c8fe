"""
鲁棒大误差配准算法。
专门处理初始误差较大的配准问题。
目标：达到PV值90μm和RMS值20μm的精度要求。
"""
import os
import numpy as np
import open3d as o3d
from typing import Dict, Any, Tuple
import time
from scipy.optimize import minimize

def load_point_cloud(file_path: str) -> o3d.geometry.PointCloud:
    """加载点云数据。"""
    pcd = o3d.geometry.PointCloud()
    
    try:
        with open(file_path, 'r') as f:
            lines = f.readlines()
        
        points = []
        for line in lines:
            values = line.strip().split()
            if len(values) >= 3:
                x, y, z = float(values[0]), float(values[1]), float(values[2])
                points.append([x, y, z])
        
        points = np.array(points)
        pcd.points = o3d.utility.Vector3dVector(points)
        print(f"成功加载点云，共 {len(points)} 个点")
    except Exception as e:
        raise ValueError(f"无法加载点云文件: {e}")
    
    return pcd

def calculate_metrics(source_points: np.ndarray, target_points: np.ndarray, 
                      trim_percentage: float = 0.0) -> Dict[str, Any]:
    """计算配准指标：PV值和RMS。"""
    target_tree = o3d.geometry.KDTreeFlann()
    target_pcd = o3d.geometry.PointCloud()
    target_pcd.points = o3d.utility.Vector3dVector(target_points)
    target_tree.set_geometry(target_pcd)
    
    distances = []
    for point in source_points:
        _, _, dist = target_tree.search_knn_vector_3d(point, 1)
        distances.append(np.sqrt(dist[0]))
    
    distances = np.array(distances)
    
    if trim_percentage > 0:
        sorted_distances = np.sort(distances)
        trim_index = int(len(sorted_distances) * (1 - trim_percentage))
        trimmed_distances = sorted_distances[:trim_index]
    else:
        trimmed_distances = distances
    
    pv_value = np.percentile(trimmed_distances, 90) * 1000  # 转换为微米
    rms = np.sqrt(np.mean(trimmed_distances ** 2)) * 1000  # 转换为微米
    
    return {
        'pv_value': pv_value,
        'rms': rms,
        'distances': trimmed_distances,
        'original_distances': distances
    }

def progressive_icp_with_large_tolerance(source: o3d.geometry.PointCloud, target: o3d.geometry.PointCloud,
                                       initial_transformation: np.ndarray) -> np.ndarray:
    """渐进式ICP，从大容差开始逐步收紧。"""
    print("  执行渐进式大容差ICP配准...")
    
    transformation = initial_transformation.copy()
    
    # 计算初始误差来确定起始容差
    source_temp = o3d.geometry.PointCloud()
    source_temp.points = o3d.utility.Vector3dVector(np.asarray(source.points))
    source_temp.transform(transformation)
    
    initial_metrics = calculate_metrics(np.asarray(source_temp.points), np.asarray(target.points))
    initial_error = initial_metrics['rms'] / 1000.0  # 转换为米
    
    print(f"    初始误差: {initial_error*1000:.2f}μm")
    
    # 根据初始误差设计渐进式参数
    stages = [
        {'voxel_size': 0.05, 'max_distance': initial_error * 2.0, 'max_iter': 30, 'name': '超粗'},
        {'voxel_size': 0.02, 'max_distance': initial_error * 1.5, 'max_iter': 50, 'name': '粗'},
        {'voxel_size': 0.01, 'max_distance': initial_error * 1.0, 'max_iter': 100, 'name': '中'},
        {'voxel_size': 0.005, 'max_distance': initial_error * 0.7, 'max_iter': 150, 'name': '细'},
        {'voxel_size': 0.002, 'max_distance': initial_error * 0.5, 'max_iter': 200, 'name': '超细'},
    ]
    
    for i, stage in enumerate(stages):
        print(f"    {stage['name']}阶段: 体素={stage['voxel_size']:.3f}, 最大距离={stage['max_distance']*1000:.1f}μm")
        
        # 下采样
        source_down = source.voxel_down_sample(stage['voxel_size'])
        target_down = target.voxel_down_sample(stage['voxel_size'])
        
        print(f"      下采样后: 源={len(source_down.points)}点, 目标={len(target_down.points)}点")
        
        # 估计法向量
        source_down.estimate_normals(
            o3d.geometry.KDTreeSearchParamHybrid(radius=stage['voxel_size'] * 2, max_nn=30))
        target_down.estimate_normals(
            o3d.geometry.KDTreeSearchParamHybrid(radius=stage['voxel_size'] * 2, max_nn=30))
        
        # 应用当前变换
        source_transformed = o3d.geometry.PointCloud()
        source_transformed.points = o3d.utility.Vector3dVector(np.asarray(source_down.points))
        source_transformed.normals = o3d.utility.Vector3dVector(np.asarray(source_down.normals))
        source_transformed.transform(transformation)
        
        # 执行ICP
        result = o3d.pipelines.registration.registration_icp(
            source_transformed, target_down,
            max_correspondence_distance=stage['max_distance'],
            init=np.identity(4),
            estimation_method=o3d.pipelines.registration.TransformationEstimationPointToPlane(),
            criteria=o3d.pipelines.registration.ICPConvergenceCriteria(max_iteration=stage['max_iter'])
        )
        
        print(f"      ICP结果: 适应度={result.fitness:.4f}, RMSE={result.inlier_rmse:.4f}")
        
        if result.fitness > 0.001:  # 非常宽松的适应度要求
            new_transformation = np.matmul(result.transformation, transformation)
            
            # 验证改进
            source_test = o3d.geometry.PointCloud()
            source_test.points = o3d.utility.Vector3dVector(np.asarray(source.points))
            source_test.transform(new_transformation)
            
            test_metrics = calculate_metrics(np.asarray(source_test.points), np.asarray(target.points))
            
            if test_metrics['rms'] <= initial_metrics['rms']:
                transformation = new_transformation
                initial_metrics = test_metrics
                print(f"      ✅ 改进: 新RMS={test_metrics['rms']:.2f}μm")
            else:
                print(f"      ❌ 无改进，保持之前变换")
        else:
            print(f"      ⚠️  适应度过低，保持之前变换")
    
    return transformation

def trimmed_icp_registration(source: o3d.geometry.PointCloud, target: o3d.geometry.PointCloud,
                           initial_transformation: np.ndarray, overlap_ratio: float = 0.7) -> np.ndarray:
    """修剪ICP配准，只使用最佳匹配的点。"""
    print(f"  执行修剪ICP配准（重叠率: {overlap_ratio:.1%}）...")
    
    transformation = initial_transformation.copy()
    
    # 多次迭代修剪ICP
    for iteration in range(5):
        print(f"    迭代 {iteration + 1}:")
        
        # 应用当前变换
        source_transformed = o3d.geometry.PointCloud()
        source_transformed.points = o3d.utility.Vector3dVector(np.asarray(source.points))
        source_transformed.transform(transformation)
        
        # 下采样以提高速度
        voxel_size = 0.005
        source_down = source_transformed.voxel_down_sample(voxel_size)
        target_down = target.voxel_down_sample(voxel_size)
        
        source_down.estimate_normals(
            o3d.geometry.KDTreeSearchParamHybrid(radius=voxel_size * 2, max_nn=30))
        target_down.estimate_normals(
            o3d.geometry.KDTreeSearchParamHybrid(radius=voxel_size * 2, max_nn=30))
        
        print(f"      处理点数: 源={len(source_down.points)}, 目标={len(target_down.points)}")
        
        # 计算点到点距离
        source_points = np.asarray(source_down.points)
        target_points = np.asarray(target_down.points)
        
        target_tree = o3d.geometry.KDTreeFlann()
        target_tree.set_geometry(target_down)
        
        distances = []
        correspondences = []
        
        for i, point in enumerate(source_points):
            _, indices, dists = target_tree.search_knn_vector_3d(point, 1)
            if len(dists) > 0:
                distances.append(np.sqrt(dists[0]))
                correspondences.append((i, indices[0]))
        
        if len(distances) == 0:
            print(f"      ❌ 没有找到对应点")
            break
        
        distances = np.array(distances)
        
        # 选择最佳匹配的点（修剪）
        num_keep = int(len(distances) * overlap_ratio)
        best_indices = np.argsort(distances)[:num_keep]
        
        print(f"      保留最佳 {num_keep}/{len(distances)} 个对应点")
        
        # 构建修剪后的点云
        trimmed_source_indices = [correspondences[i][0] for i in best_indices]
        trimmed_target_indices = [correspondences[i][1] for i in best_indices]
        
        trimmed_source = o3d.geometry.PointCloud()
        trimmed_source.points = o3d.utility.Vector3dVector(source_points[trimmed_source_indices])
        trimmed_source.normals = o3d.utility.Vector3dVector(np.asarray(source_down.normals)[trimmed_source_indices])
        
        trimmed_target = o3d.geometry.PointCloud()
        trimmed_target.points = o3d.utility.Vector3dVector(target_points[trimmed_target_indices])
        trimmed_target.normals = o3d.utility.Vector3dVector(np.asarray(target_down.normals)[trimmed_target_indices])
        
        # 执行ICP
        result = o3d.pipelines.registration.registration_icp(
            trimmed_source, trimmed_target,
            max_correspondence_distance=voxel_size * 2,
            init=np.identity(4),
            estimation_method=o3d.pipelines.registration.TransformationEstimationPointToPlane(),
            criteria=o3d.pipelines.registration.ICPConvergenceCriteria(max_iteration=100)
        )
        
        print(f"      修剪ICP结果: 适应度={result.fitness:.4f}, RMSE={result.inlier_rmse:.4f}")
        
        if result.fitness > 0.5:
            new_transformation = np.matmul(result.transformation, transformation)
            
            # 验证改进
            source_test = o3d.geometry.PointCloud()
            source_test.points = o3d.utility.Vector3dVector(np.asarray(source.points))
            source_test.transform(new_transformation)
            
            test_metrics = calculate_metrics(np.asarray(source_test.points), np.asarray(target.points))
            current_metrics = calculate_metrics(np.asarray(source_transformed.points), np.asarray(target.points))
            
            if test_metrics['rms'] < current_metrics['rms']:
                transformation = new_transformation
                print(f"      ✅ 改进: RMS {current_metrics['rms']:.2f} → {test_metrics['rms']:.2f}μm")
                
                improvement = (current_metrics['rms'] - test_metrics['rms']) / current_metrics['rms']
                if improvement < 0.01:  # 改进小于1%
                    print(f"      改进幅度很小，停止迭代")
                    break
            else:
                print(f"      ❌ 无改进，停止迭代")
                break
        else:
            print(f"      ❌ 适应度过低，停止迭代")
            break
    
    return transformation

def fine_tune_with_small_perturbations(source: o3d.geometry.PointCloud, target: o3d.geometry.PointCloud,
                                     initial_transformation: np.ndarray) -> np.ndarray:
    """使用小扰动进行精细调整。"""
    print("  执行小扰动精细调整...")
    
    source_points = np.asarray(source.points)
    target_points = np.asarray(target.points)
    
    # 下采样以提高速度
    if len(source_points) > 10000:
        indices = np.random.choice(len(source_points), 10000, replace=False)
        source_sample = source_points[indices]
    else:
        source_sample = source_points
    
    print(f"    使用 {len(source_sample)} 个源点进行精细调整")
    
    # 构建目标点云的KD树
    target_tree = o3d.geometry.KDTreeFlann()
    target_pcd = o3d.geometry.PointCloud()
    target_pcd.points = o3d.utility.Vector3dVector(target_points)
    target_tree.set_geometry(target_pcd)
    
    def apply_transformation(points, transformation):
        """应用变换矩阵。"""
        homogeneous_points = np.hstack([points, np.ones((len(points), 1))])
        transformed = (transformation @ homogeneous_points.T).T
        return transformed[:, :3]
    
    def objective_function(params):
        """目标函数：最小化点到点距离。"""
        try:
            # 参数: [tx, ty, tz, rx, ry, rz] (小扰动)
            tx, ty, tz, rx, ry, rz = params
            
            # 构建扰动变换矩阵
            perturbation = np.eye(4)
            
            # 小角度旋转矩阵
            perturbation[:3, :3] = np.array([
                [1, -rz, ry],
                [rz, 1, -rx],
                [-ry, rx, 1]
            ])
            perturbation[:3, 3] = [tx, ty, tz]
            
            # 应用扰动
            perturbed_transformation = perturbation @ initial_transformation
            transformed_source = apply_transformation(source_sample, perturbed_transformation)
            
            # 计算距离
            distances = []
            for point in transformed_source:
                _, _, dist = target_tree.search_knn_vector_3d(point, 1)
                if len(dist) > 0:
                    distances.append(np.sqrt(dist[0]))
            
            if len(distances) == 0:
                return 1e6
            
            distances = np.array(distances)
            
            # 使用鲁棒损失函数
            huber_delta = 0.002  # 2mm
            residuals = distances
            huber_loss = np.where(residuals <= huber_delta,
                                0.5 * residuals**2,
                                huber_delta * (residuals - 0.5 * huber_delta))
            
            return np.mean(huber_loss)
        except Exception as e:
            return 1e6
    
    # 多次随机初始化优化
    best_transformation = initial_transformation
    best_objective = float('inf')
    
    for attempt in range(3):
        print(f"    优化尝试 {attempt + 1}/3:")
        
        # 随机初始扰动
        initial_params = np.random.normal(0, 0.001, 6)  # 小的随机初始值
        
        try:
            result = minimize(objective_function, initial_params, 
                             method='L-BFGS-B',
                             bounds=[(-0.005, 0.005)] * 6,  # 限制扰动范围
                             options={'maxiter': 50})
            
            if result.success and result.fun < best_objective:
                tx, ty, tz, rx, ry, rz = result.x
                
                perturbation = np.eye(4)
                perturbation[:3, :3] = np.array([
                    [1, -rz, ry],
                    [rz, 1, -rx],
                    [-ry, rx, 1]
                ])
                perturbation[:3, 3] = [tx, ty, tz]
                
                best_transformation = perturbation @ initial_transformation
                best_objective = result.fun
                
                print(f"      成功: 目标值={result.fun:.6f}")
                print(f"      扰动: 平移=[{tx:.6f}, {ty:.6f}, {tz:.6f}], "
                      f"旋转=[{rx:.6f}, {ry:.6f}, {rz:.6f}]")
            else:
                print(f"      失败或无改进")
        except Exception as e:
            print(f"      异常: {e}")
    
    if best_objective < float('inf'):
        print(f"    最佳优化结果: 目标值={best_objective:.6f}")
        return best_transformation
    else:
        print(f"    所有优化尝试都失败，返回初始变换")
        return initial_transformation

def main():
    """主函数。"""
    source_file = '点云数据.txt'
    target_file = '面型扫描.txt'
    coarse_rt_file = '粗配准RT.txt'
    results_dir = 'robust_large_error_results'
    
    os.makedirs(results_dir, exist_ok=True)
    
    print("=== 鲁棒大误差配准算法 ===")
    
    # 1. 加载数据
    print("\n1. 加载数据...")
    source = load_point_cloud(source_file)
    target = load_point_cloud(target_file)
    coarse_transformation = np.loadtxt(coarse_rt_file)
    
    # 2. 应用粗配准
    print("\n2. 应用粗配准...")
    source_coarse = o3d.geometry.PointCloud()
    source_coarse.points = o3d.utility.Vector3dVector(np.asarray(source.points))
    source_coarse.transform(coarse_transformation)
    
    coarse_metrics = calculate_metrics(np.asarray(source_coarse.points), np.asarray(target.points))
    print(f"粗配准后: PV={coarse_metrics['pv_value']:.2f}μm, RMS={coarse_metrics['rms']:.2f}μm")
    
    # 3. 渐进式大容差ICP
    print("\n3. 渐进式大容差ICP...")
    start_time = time.time()
    progressive_transformation = progressive_icp_with_large_tolerance(source_coarse, target, np.identity(4))
    progressive_time = time.time() - start_time
    
    progressive_final = np.matmul(progressive_transformation, coarse_transformation)
    
    source_progressive = o3d.geometry.PointCloud()
    source_progressive.points = o3d.utility.Vector3dVector(np.asarray(source.points))
    source_progressive.transform(progressive_final)
    
    progressive_metrics = calculate_metrics(np.asarray(source_progressive.points), np.asarray(target.points))
    print(f"渐进式ICP结果: PV={progressive_metrics['pv_value']:.2f}μm, RMS={progressive_metrics['rms']:.2f}μm")
    
    # 4. 修剪ICP配准
    print("\n4. 修剪ICP配准...")
    start_time = time.time()
    trimmed_transformation = trimmed_icp_registration(source_progressive, target, np.identity(4))
    trimmed_time = time.time() - start_time
    
    trimmed_final = np.matmul(trimmed_transformation, progressive_final)
    
    source_trimmed = o3d.geometry.PointCloud()
    source_trimmed.points = o3d.utility.Vector3dVector(np.asarray(source.points))
    source_trimmed.transform(trimmed_final)
    
    trimmed_metrics = calculate_metrics(np.asarray(source_trimmed.points), np.asarray(target.points))
    print(f"修剪ICP结果: PV={trimmed_metrics['pv_value']:.2f}μm, RMS={trimmed_metrics['rms']:.2f}μm")
    
    # 5. 精细调整
    print("\n5. 精细调整...")
    start_time = time.time()
    fine_tune_transformation = fine_tune_with_small_perturbations(source_trimmed, target, np.identity(4))
    fine_tune_time = time.time() - start_time
    
    final_transformation = np.matmul(fine_tune_transformation, trimmed_final)
    
    # 6. 计算最终结果
    print("\n6. 计算最终结果...")
    source_final = o3d.geometry.PointCloud()
    source_final.points = o3d.utility.Vector3dVector(np.asarray(source.points))
    source_final.transform(final_transformation)
    
    final_metrics = calculate_metrics(np.asarray(source_final.points), np.asarray(target.points))
    
    # 7. 结果总结
    print("\n7. 结果总结:")
    print("=" * 70)
    print(f"{'阶段':<20} {'PV值(μm)':<12} {'RMS值(μm)':<12} {'耗时(s)':<10} {'改进'}")
    print("-" * 70)
    
    stages = [
        ('粗配准', coarse_metrics, 0),
        ('渐进式ICP', progressive_metrics, progressive_time),
        ('修剪ICP', trimmed_metrics, trimmed_time),
        ('精细调整', final_metrics, fine_tune_time)
    ]
    
    for stage_name, metrics, stage_time in stages:
        if stage_name == '粗配准':
            improvement = "基线"
        else:
            pv_imp = (coarse_metrics['pv_value'] - metrics['pv_value']) / coarse_metrics['pv_value'] * 100
            rms_imp = (coarse_metrics['rms'] - metrics['rms']) / coarse_metrics['rms'] * 100
            improvement = f"PV:{pv_imp:+.1f}% RMS:{rms_imp:+.1f}%"
        
        print(f"{stage_name:<20} {metrics['pv_value']:<12.2f} {metrics['rms']:<12.2f} {stage_time:<10.2f} {improvement}")
    
    # 检查目标精度
    target_pv = 90.0
    target_rms = 20.0
    
    print(f"\n目标精度: PV≤{target_pv}μm, RMS≤{target_rms}μm")
    
    if final_metrics['pv_value'] <= target_pv and final_metrics['rms'] <= target_rms:
        print(f"🎉 成功达到目标精度！")
    else:
        print(f"❌ 未达到目标精度")
        print(f"   PV值: {final_metrics['pv_value']:.2f} {'≤' if final_metrics['pv_value'] <= target_pv else '>'} {target_pv}μm")
        print(f"   RMS值: {final_metrics['rms']:.2f} {'≤' if final_metrics['rms'] <= target_rms else '>'} {target_rms}μm")
        
        # 测试裁剪效果
        print(f"\n测试边缘裁剪效果:")
        for trim_percentage in [0.05, 0.1, 0.15, 0.2, 0.25, 0.3, 0.35, 0.4, 0.45, 0.5]:
            trimmed_metrics = calculate_metrics(
                np.asarray(source_final.points), 
                np.asarray(target.points),
                trim_percentage=trim_percentage
            )
            print(f"  裁剪 {trim_percentage*100:.0f}%: PV={trimmed_metrics['pv_value']:.2f}μm, RMS={trimmed_metrics['rms']:.2f}μm")
            
            if trimmed_metrics['pv_value'] <= target_pv and trimmed_metrics['rms'] <= target_rms:
                print(f"    🎉 裁剪 {trim_percentage*100:.0f}% 后达到目标精度！")
                break
    
    # 8. 保存结果
    print(f"\n8. 保存结果...")
    
    np.savetxt(os.path.join(results_dir, 'final_transformation.txt'), final_transformation)
    np.savetxt(os.path.join(results_dir, 'progressive_transformation.txt'), progressive_transformation)
    np.savetxt(os.path.join(results_dir, 'trimmed_transformation.txt'), trimmed_transformation)
    np.savetxt(os.path.join(results_dir, 'fine_tune_transformation.txt'), fine_tune_transformation)
    
    o3d.io.write_point_cloud(os.path.join(results_dir, 'final_result.ply'), source_final)
    
    print("鲁棒大误差配准完成!")

if __name__ == "__main__":
    main()
