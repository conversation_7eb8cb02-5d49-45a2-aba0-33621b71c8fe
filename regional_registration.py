"""
分区域配准和数据质量分析脚本。
尝试通过分区域配准和深度数据分析来突破精度瓶颈。
目标：达到PV值90μm和RMS值20μm的精度要求。
"""
import os
import numpy as np
import open3d as o3d
from typing import Dict, Any, <PERSON><PERSON>, List
import time
from sklearn.cluster import KMeans
import matplotlib.pyplot as plt

def load_point_cloud(file_path: str) -> o3d.geometry.PointCloud:
    """加载点云数据。"""
    pcd = o3d.geometry.PointCloud()
    
    try:
        with open(file_path, 'r') as f:
            lines = f.readlines()
        
        points = []
        for line in lines:
            values = line.strip().split()
            if len(values) >= 3:
                x, y, z = float(values[0]), float(values[1]), float(values[2])
                points.append([x, y, z])
        
        points = np.array(points)
        pcd.points = o3d.utility.Vector3dVector(points)
        print(f"成功加载点云，共 {len(points)} 个点")
    except Exception as e:
        raise ValueError(f"无法加载点云文件: {e}")
    
    return pcd

def calculate_metrics(source_points: np.ndarray, target_points: np.ndarray, 
                      trim_percentage: float = 0.0) -> Dict[str, Any]:
    """计算配准指标：PV值和RMS。"""
    target_tree = o3d.geometry.KDTreeFlann()
    target_pcd = o3d.geometry.PointCloud()
    target_pcd.points = o3d.utility.Vector3dVector(target_points)
    target_tree.set_geometry(target_pcd)
    
    distances = []
    for point in source_points:
        _, _, dist = target_tree.search_knn_vector_3d(point, 1)
        distances.append(np.sqrt(dist[0]))
    
    distances = np.array(distances)
    
    if trim_percentage > 0:
        sorted_distances = np.sort(distances)
        trim_index = int(len(sorted_distances) * (1 - trim_percentage))
        trimmed_distances = sorted_distances[:trim_index]
    else:
        trimmed_distances = distances
    
    pv_value = np.percentile(trimmed_distances, 90) * 1000  # 转换为微米
    rms = np.sqrt(np.mean(trimmed_distances ** 2)) * 1000  # 转换为微米
    
    return {
        'pv_value': pv_value,
        'rms': rms,
        'distances': trimmed_distances,
        'original_distances': distances
    }

def analyze_point_cloud_quality(source: o3d.geometry.PointCloud, target: o3d.geometry.PointCloud, 
                               transformation: np.ndarray) -> Dict[str, Any]:
    """分析点云质量和分布特征。"""
    print("  分析点云质量...")
    
    # 应用变换
    source_transformed = o3d.geometry.PointCloud()
    source_transformed.points = o3d.utility.Vector3dVector(np.asarray(source.points))
    source_transformed.transform(transformation)
    
    source_points = np.asarray(source_transformed.points)
    target_points = np.asarray(target.points)
    
    # 计算点云边界
    source_bounds = {
        'min': np.min(source_points, axis=0),
        'max': np.max(source_points, axis=0),
        'center': np.mean(source_points, axis=0),
        'std': np.std(source_points, axis=0)
    }
    
    target_bounds = {
        'min': np.min(target_points, axis=0),
        'max': np.max(target_points, axis=0),
        'center': np.mean(target_points, axis=0),
        'std': np.std(target_points, axis=0)
    }
    
    # 计算点密度
    source_volume = np.prod(source_bounds['max'] - source_bounds['min'])
    target_volume = np.prod(target_bounds['max'] - target_bounds['min'])
    
    source_density = len(source_points) / source_volume if source_volume > 0 else 0
    target_density = len(target_points) / target_volume if target_volume > 0 else 0
    
    # 计算距离分布
    target_tree = o3d.geometry.KDTreeFlann()
    target_pcd = o3d.geometry.PointCloud()
    target_pcd.points = o3d.utility.Vector3dVector(target_points)
    target_tree.set_geometry(target_pcd)
    
    distances = []
    for point in source_points:
        _, _, dist = target_tree.search_knn_vector_3d(point, 1)
        distances.append(np.sqrt(dist[0]))
    
    distances = np.array(distances)
    
    analysis = {
        'source_bounds': source_bounds,
        'target_bounds': target_bounds,
        'source_density': source_density,
        'target_density': target_density,
        'distance_stats': {
            'mean': np.mean(distances),
            'std': np.std(distances),
            'min': np.min(distances),
            'max': np.max(distances),
            'percentiles': {
                '50': np.percentile(distances, 50),
                '75': np.percentile(distances, 75),
                '90': np.percentile(distances, 90),
                '95': np.percentile(distances, 95),
                '99': np.percentile(distances, 99)
            }
        },
        'distances': distances
    }
    
    print(f"    源点云密度: {source_density:.2f} 点/立方单位")
    print(f"    目标点云密度: {target_density:.2f} 点/立方单位")
    print(f"    距离统计: 均值={analysis['distance_stats']['mean']*1000:.2f}μm, "
          f"标准差={analysis['distance_stats']['std']*1000:.2f}μm")
    print(f"    距离百分位: 50%={analysis['distance_stats']['percentiles']['50']*1000:.2f}μm, "
          f"90%={analysis['distance_stats']['percentiles']['90']*1000:.2f}μm, "
          f"99%={analysis['distance_stats']['percentiles']['99']*1000:.2f}μm")
    
    return analysis

def divide_point_cloud_regions(points: np.ndarray, num_regions: int = 9) -> List[np.ndarray]:
    """将点云分成多个区域。"""
    print(f"  将点云分成 {num_regions} 个区域...")
    
    # 计算边界
    min_bounds = np.min(points, axis=0)
    max_bounds = np.max(points, axis=0)
    
    # 计算每个维度的分割数
    if num_regions == 4:
        nx, ny = 2, 2
    elif num_regions == 9:
        nx, ny = 3, 3
    elif num_regions == 16:
        nx, ny = 4, 4
    else:
        nx = ny = int(np.sqrt(num_regions))
    
    # 计算分割边界
    x_edges = np.linspace(min_bounds[0], max_bounds[0], nx + 1)
    y_edges = np.linspace(min_bounds[1], max_bounds[1], ny + 1)
    
    regions = []
    region_info = []
    
    for i in range(nx):
        for j in range(ny):
            # 定义区域边界
            x_min, x_max = x_edges[i], x_edges[i + 1]
            y_min, y_max = y_edges[j], y_edges[j + 1]
            
            # 选择在该区域内的点
            mask = ((points[:, 0] >= x_min) & (points[:, 0] <= x_max) &
                   (points[:, 1] >= y_min) & (points[:, 1] <= y_max))
            
            region_points = points[mask]
            
            if len(region_points) > 100:  # 只保留有足够点的区域
                regions.append(region_points)
                region_info.append({
                    'id': len(regions) - 1,
                    'bounds': [(x_min, x_max), (y_min, y_max)],
                    'num_points': len(region_points),
                    'center': np.mean(region_points, axis=0)
                })
                print(f"    区域 {len(regions)}: {len(region_points)} 个点")
    
    print(f"  总共创建了 {len(regions)} 个有效区域")
    return regions, region_info

def regional_icp_registration(source_regions: List[np.ndarray], target_regions: List[np.ndarray],
                            initial_transformation: np.ndarray) -> np.ndarray:
    """分区域ICP配准。"""
    print("  执行分区域ICP配准...")
    
    if len(source_regions) != len(target_regions):
        print(f"    ⚠️  源区域数({len(source_regions)})与目标区域数({len(target_regions)})不匹配")
        return initial_transformation
    
    regional_transformations = []
    weights = []
    
    for i, (src_region, tgt_region) in enumerate(zip(source_regions, target_regions)):
        print(f"    配准区域 {i+1}/{len(source_regions)}: {len(src_region)} vs {len(tgt_region)} 点")
        
        if len(src_region) < 50 or len(tgt_region) < 50:
            print(f"      跳过（点数太少）")
            continue
        
        # 创建区域点云
        src_pcd = o3d.geometry.PointCloud()
        src_pcd.points = o3d.utility.Vector3dVector(src_region)
        
        tgt_pcd = o3d.geometry.PointCloud()
        tgt_pcd.points = o3d.utility.Vector3dVector(tgt_region)
        
        # 预处理
        voxel_size = 0.005
        src_pcd = src_pcd.voxel_down_sample(voxel_size)
        tgt_pcd = tgt_pcd.voxel_down_sample(voxel_size)
        
        src_pcd.estimate_normals(
            o3d.geometry.KDTreeSearchParamHybrid(radius=voxel_size * 2, max_nn=30))
        tgt_pcd.estimate_normals(
            o3d.geometry.KDTreeSearchParamHybrid(radius=voxel_size * 2, max_nn=30))
        
        # 执行ICP
        try:
            result = o3d.pipelines.registration.registration_icp(
                src_pcd, tgt_pcd,
                max_correspondence_distance=voxel_size * 3,
                init=np.identity(4),
                estimation_method=o3d.pipelines.registration.TransformationEstimationPointToPlane(),
                criteria=o3d.pipelines.registration.ICPConvergenceCriteria(max_iteration=100)
            )
            
            if result.fitness > 0.1:  # 只使用高质量的配准结果
                regional_transformations.append(result.transformation)
                weights.append(result.fitness * len(src_region))
                print(f"      成功: 适应度={result.fitness:.4f}, RMSE={result.inlier_rmse:.4f}")
            else:
                print(f"      失败: 适应度过低({result.fitness:.4f})")
        except Exception as e:
            print(f"      异常: {e}")
    
    if not regional_transformations:
        print("    ⚠️  没有成功的区域配准，返回初始变换")
        return initial_transformation
    
    # 计算加权平均变换
    print(f"    合并 {len(regional_transformations)} 个区域配准结果...")
    
    weights = np.array(weights)
    weights = weights / np.sum(weights)  # 归一化权重
    
    # 分别处理旋转和平移
    rotations = []
    translations = []
    
    for T in regional_transformations:
        rotations.append(T[:3, :3])
        translations.append(T[:3, 3])
    
    # 加权平均平移
    avg_translation = np.average(translations, weights=weights, axis=0)
    
    # 旋转矩阵的加权平均（使用四元数）
    from scipy.spatial.transform import Rotation
    
    quaternions = []
    for R in rotations:
        r = Rotation.from_matrix(R)
        quaternions.append(r.as_quat())
    
    # 加权平均四元数
    avg_quat = np.average(quaternions, weights=weights, axis=0)
    avg_quat = avg_quat / np.linalg.norm(avg_quat)  # 归一化
    
    avg_rotation = Rotation.from_quat(avg_quat).as_matrix()
    
    # 构建最终变换矩阵
    final_transformation = np.eye(4)
    final_transformation[:3, :3] = avg_rotation
    final_transformation[:3, 3] = avg_translation
    
    return final_transformation

def adaptive_outlier_removal(source: o3d.geometry.PointCloud, target: o3d.geometry.PointCloud,
                           transformation: np.ndarray, distance_threshold: float = 0.01) -> Tuple[o3d.geometry.PointCloud, o3d.geometry.PointCloud]:
    """自适应离群点移除。"""
    print(f"  执行自适应离群点移除（阈值: {distance_threshold*1000:.1f}μm）...")
    
    # 应用变换
    source_transformed = o3d.geometry.PointCloud()
    source_transformed.points = o3d.utility.Vector3dVector(np.asarray(source.points))
    source_transformed.transform(transformation)
    
    source_points = np.asarray(source_transformed.points)
    target_points = np.asarray(target.points)
    
    # 计算每个源点到目标点云的距离
    target_tree = o3d.geometry.KDTreeFlann()
    target_pcd = o3d.geometry.PointCloud()
    target_pcd.points = o3d.utility.Vector3dVector(target_points)
    target_tree.set_geometry(target_pcd)
    
    valid_source_indices = []
    valid_target_indices = []
    
    for i, point in enumerate(source_points):
        _, indices, distances = target_tree.search_knn_vector_3d(point, 1)
        if len(distances) > 0 and np.sqrt(distances[0]) < distance_threshold:
            valid_source_indices.append(i)
            valid_target_indices.append(indices[0])
    
    print(f"    保留源点: {len(valid_source_indices)}/{len(source_points)} ({len(valid_source_indices)/len(source_points)*100:.1f}%)")
    
    # 创建过滤后的点云
    if valid_source_indices:
        filtered_source = o3d.geometry.PointCloud()
        filtered_source.points = o3d.utility.Vector3dVector(source_points[valid_source_indices])
        
        # 对应的目标点
        corresponding_target_points = target_points[valid_target_indices]
        filtered_target = o3d.geometry.PointCloud()
        filtered_target.points = o3d.utility.Vector3dVector(corresponding_target_points)
        
        return filtered_source, filtered_target
    else:
        return source_transformed, target

def main():
    """主函数。"""
    source_file = '点云数据.txt'
    target_file = '面型扫描.txt'
    coarse_rt_file = '粗配准RT.txt'
    results_dir = 'regional_registration_results'
    
    os.makedirs(results_dir, exist_ok=True)
    
    print("=== 分区域配准和数据质量分析 ===")
    
    # 1. 加载数据
    print("\n1. 加载数据...")
    source = load_point_cloud(source_file)
    target = load_point_cloud(target_file)
    coarse_transformation = np.loadtxt(coarse_rt_file)
    
    # 2. 应用粗配准
    print("\n2. 应用粗配准...")
    source_coarse = o3d.geometry.PointCloud()
    source_coarse.points = o3d.utility.Vector3dVector(np.asarray(source.points))
    source_coarse.transform(coarse_transformation)
    
    coarse_metrics = calculate_metrics(np.asarray(source_coarse.points), np.asarray(target.points))
    print(f"粗配准后: PV={coarse_metrics['pv_value']:.2f}μm, RMS={coarse_metrics['rms']:.2f}μm")
    
    # 3. 数据质量分析
    print("\n3. 数据质量分析...")
    quality_analysis = analyze_point_cloud_quality(source, target, coarse_transformation)
    
    # 4. 分区域配准
    print("\n4. 分区域配准...")
    
    # 分割点云
    source_regions, source_info = divide_point_cloud_regions(np.asarray(source_coarse.points), num_regions=9)
    target_regions, target_info = divide_point_cloud_regions(np.asarray(target.points), num_regions=9)
    
    # 执行分区域配准
    start_time = time.time()
    regional_transformation = regional_icp_registration(source_regions, target_regions, np.identity(4))
    regional_time = time.time() - start_time
    
    # 应用区域配准结果
    regional_final_transformation = np.matmul(regional_transformation, coarse_transformation)
    
    source_regional = o3d.geometry.PointCloud()
    source_regional.points = o3d.utility.Vector3dVector(np.asarray(source.points))
    source_regional.transform(regional_final_transformation)
    
    regional_metrics = calculate_metrics(np.asarray(source_regional.points), np.asarray(target.points))
    print(f"分区域配准结果: PV={regional_metrics['pv_value']:.2f}μm, RMS={regional_metrics['rms']:.2f}μm")
    
    # 5. 自适应离群点移除 + 精细配准
    print("\n5. 自适应离群点移除 + 精细配准...")
    
    # 移除离群点
    filtered_source, filtered_target = adaptive_outlier_removal(
        source, target, regional_final_transformation, distance_threshold=0.005
    )
    
    # 对过滤后的点云进行精细配准
    if len(np.asarray(filtered_source.points)) > 1000:
        voxel_size = 0.002
        filtered_source_down = filtered_source.voxel_down_sample(voxel_size)
        filtered_target_down = filtered_target.voxel_down_sample(voxel_size)
        
        filtered_source_down.estimate_normals(
            o3d.geometry.KDTreeSearchParamHybrid(radius=voxel_size * 2, max_nn=30))
        filtered_target_down.estimate_normals(
            o3d.geometry.KDTreeSearchParamHybrid(radius=voxel_size * 2, max_nn=30))
        
        fine_result = o3d.pipelines.registration.registration_icp(
            filtered_source_down, filtered_target_down,
            max_correspondence_distance=voxel_size * 2,
            init=np.identity(4),
            estimation_method=o3d.pipelines.registration.TransformationEstimationPointToPlane(),
            criteria=o3d.pipelines.registration.ICPConvergenceCriteria(max_iteration=300)
        )
        
        print(f"精细配准: 适应度={fine_result.fitness:.4f}, RMSE={fine_result.inlier_rmse:.4f}")
        
        # 最终变换
        final_transformation = np.matmul(fine_result.transformation, regional_final_transformation)
    else:
        final_transformation = regional_final_transformation
        print("过滤后点数太少，跳过精细配准")
    
    # 6. 计算最终结果
    print("\n6. 计算最终结果...")
    source_final = o3d.geometry.PointCloud()
    source_final.points = o3d.utility.Vector3dVector(np.asarray(source.points))
    source_final.transform(final_transformation)
    
    final_metrics = calculate_metrics(np.asarray(source_final.points), np.asarray(target.points))
    
    # 7. 结果总结
    print("\n7. 结果总结:")
    print("=" * 60)
    print(f"{'阶段':<20} {'PV值(μm)':<12} {'RMS值(μm)':<12} {'改进'}")
    print("-" * 60)
    
    stages = [
        ('粗配准', coarse_metrics),
        ('分区域配准', regional_metrics),
        ('最终结果', final_metrics)
    ]
    
    for stage_name, metrics in stages:
        if stage_name == '粗配准':
            improvement = "基线"
        else:
            pv_imp = (coarse_metrics['pv_value'] - metrics['pv_value']) / coarse_metrics['pv_value'] * 100
            rms_imp = (coarse_metrics['rms'] - metrics['rms']) / coarse_metrics['rms'] * 100
            improvement = f"PV:{pv_imp:+.1f}% RMS:{rms_imp:+.1f}%"
        
        print(f"{stage_name:<20} {metrics['pv_value']:<12.2f} {metrics['rms']:<12.2f} {improvement}")
    
    # 检查目标精度
    target_pv = 90.0
    target_rms = 20.0
    
    print(f"\n目标精度: PV≤{target_pv}μm, RMS≤{target_rms}μm")
    
    if final_metrics['pv_value'] <= target_pv and final_metrics['rms'] <= target_rms:
        print(f"🎉 成功达到目标精度！")
    else:
        print(f"❌ 未达到目标精度")
        print(f"   PV值: {final_metrics['pv_value']:.2f} {'≤' if final_metrics['pv_value'] <= target_pv else '>'} {target_pv}μm")
        print(f"   RMS值: {final_metrics['rms']:.2f} {'≤' if final_metrics['rms'] <= target_rms else '>'} {target_rms}μm")
        
        # 测试裁剪效果
        print(f"\n测试边缘裁剪效果:")
        for trim_percentage in [0.1, 0.2, 0.3, 0.4, 0.5]:
            trimmed_metrics = calculate_metrics(
                np.asarray(source_final.points), 
                np.asarray(target.points),
                trim_percentage=trim_percentage
            )
            print(f"  裁剪 {trim_percentage*100:.0f}%: PV={trimmed_metrics['pv_value']:.2f}μm, RMS={trimmed_metrics['rms']:.2f}μm")
            
            if trimmed_metrics['pv_value'] <= target_pv and trimmed_metrics['rms'] <= target_rms:
                print(f"    🎉 裁剪 {trim_percentage*100:.0f}% 后达到目标精度！")
                break
    
    # 8. 保存结果
    print(f"\n8. 保存结果...")
    
    np.savetxt(os.path.join(results_dir, 'final_transformation.txt'), final_transformation)
    np.savetxt(os.path.join(results_dir, 'regional_transformation.txt'), regional_transformation)
    
    o3d.io.write_point_cloud(os.path.join(results_dir, 'final_result.ply'), source_final)
    
    # 保存详细报告
    with open(os.path.join(results_dir, 'regional_analysis_report.txt'), 'w', encoding='utf-8') as f:
        f.write("分区域配准和数据质量分析报告\n")
        f.write("=" * 50 + "\n\n")
        
        f.write("数据质量分析:\n")
        f.write(f"  源点云密度: {quality_analysis['source_density']:.2f} 点/立方单位\n")
        f.write(f"  目标点云密度: {quality_analysis['target_density']:.2f} 点/立方单位\n")
        f.write(f"  距离统计: 均值={quality_analysis['distance_stats']['mean']*1000:.2f}μm, "
                f"标准差={quality_analysis['distance_stats']['std']*1000:.2f}μm\n\n")
        
        f.write("配准结果:\n")
        for stage_name, metrics in stages:
            f.write(f"  {stage_name}: PV={metrics['pv_value']:.2f}μm, RMS={metrics['rms']:.2f}μm\n")
        
        f.write(f"\n目标精度: PV≤{target_pv}μm, RMS≤{target_rms}μm\n")
        if final_metrics['pv_value'] <= target_pv and final_metrics['rms'] <= target_rms:
            f.write("🎉 成功达到目标精度！\n")
        else:
            f.write("❌ 未达到目标精度\n")
    
    print("分区域配准和数据质量分析完成!")

if __name__ == "__main__":
    main()
